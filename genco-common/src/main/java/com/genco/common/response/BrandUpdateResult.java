package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "BrandUpdateResult", description = "品牌批量更新结果")
public class BrandUpdateResult {
    @ApiModelProperty(value = "品牌ID")
    private Integer id;

    @ApiModelProperty(value = "是否更新成功")
    private Boolean success;

    @ApiModelProperty(value = "失败原因")
    private String message;
} 