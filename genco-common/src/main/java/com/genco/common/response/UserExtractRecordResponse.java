package com.genco.common.response;

import com.genco.common.model.finance.UserExtract;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 提现记录响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserExtractRecordResponse对象", description="用户提现记录对象")
public class UserExtractRecordResponse {

    private static final long serialVersionUID=1L;

    public UserExtractRecordResponse() {}
    public UserExtractRecordResponse(String date, List<UserExtract> list) {
        this.date = date;
        this.list = list;
    }

    @ApiModelProperty(value = "月份")
    private String date;

    @ApiModelProperty(value = "数据")
    private List<UserExtract> list;
}
