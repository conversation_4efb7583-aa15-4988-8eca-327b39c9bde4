PODS:
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - tiktok_sdk_v2 (0.0.1):
    - Flutter
    - TikTokOpenAuthSDK (~> 2.5.0)
    - TikTokOpenSDKCore (~> 2.5.0)
    - TikTokOpenShareSDK (~> 2.5.0)
  - TikTokOpenAuthSDK (2.5.0):
    - TikTokOpenAuthSDK/Auth (= 2.5.0)
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenAuthSDK/Auth (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenSDKCore (2.5.0):
    - TikTokOpenSDKCore/Core (= 2.5.0)
  - TikTokOpenSDKCore/Core (2.5.0)
  - TikTokOpenShareSDK (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
    - TikTokOpenShareSDK/Share (= 2.5.0)
  - TikTokOpenShareSDK/Share (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tiktok_sdk_v2 (from `.symlinks/plugins/tiktok_sdk_v2/ios`)
  - TikTokOpenAuthSDK
  - TikTokOpenSDKCore
  - TikTokOpenShareSDK
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - TikTokOpenAuthSDK
    - TikTokOpenSDKCore
    - TikTokOpenShareSDK

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tiktok_sdk_v2:
    :path: ".symlinks/plugins/tiktok_sdk_v2/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  tiktok_sdk_v2: 9112a694cee7dbf0c90073a4a066f8f1cc73d79c
  TikTokOpenAuthSDK: 35d99f5778b9635ab983bb25c4acf6ccad4404a9
  TikTokOpenSDKCore: e6f34e48bd6e85e4d94f9c04782c13d5defafb55
  TikTokOpenShareSDK: a7da017bc66c28d0aefea9342c0cfcc7e52ea2b7
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: d136785f2e14359d1baf5cebe9fe220cbc78733b

COCOAPODS: 1.14.3
