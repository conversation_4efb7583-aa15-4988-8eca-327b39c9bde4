import Flutter
import T<PERSON><PERSON>okOpenShareSDK
import TikTokOpenAuthSDK
import UIKit
import Tik<PERSON>okOpenSDKCore

let redirectUrl = "https://genconusantara.com/auth/callback"

@main
@objc class AppDelegate: FlutterAppDelegate {
    private var controller:FlutterViewController!
    private var tiktokLoginResult: FlutterResult? = nil
    
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
      controller = self.window.rootViewController as? FlutterViewController
      registerTiktokLogin(controller as! FlutterBinaryMessenger)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
    
  // Add this function
  override func application(
      _ app: UIApplication,
      open url: URL,
      options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bo<PERSON> {
      print("url:\(url)")
//      handleTiktokLogin(url: url)
      if TikTokURLHandler.handleOpenURL(url) {
          return true
      }
      return false
  }
    
  
  // Add this function
  override func application(
      _ application: UIApplication,
      continue userActivity: NSUserActivity,
      restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {
      print("webpageURL:\(String(describing: userActivity.webpageURL))")
//      if let url = userActivity.webpageURL {
//          handleTiktokLogin(url: url)
//      }
      if (TikTokURLHandler.handleOpenURL(userActivity.webpageURL)) {
          return true
      }
      return false
  }
    
    private func handleTiktokLogin(url: URL) {
        let result = parseAllParameters(from: url.absoluteString)
        if let errorCodeValue = result["error_code"]?.first, let errorCode = Int(errorCodeValue) {
            var value: [String: Any] = [:]
            if errorCode == 0 {
                value["success"] = true
                value["result"] = result["code"]?.first ?? ""
            } else {
                value["success"] = false
                value["error"] = result["error_code"]
            }
            tiktokLoginResult?(value)
        }
    }
    
    private func parseAllParameters(from urlString: String) -> [String: [String]] {
        guard let url = URL(string: urlString),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
              let queryItems = components.queryItems else {
            return [:]
        }
        
        var parameters: [String: [String]] = [:]
        for item in queryItems {
            if let value = item.value {
                parameters[item.name, default: []].append(value)
            }
        }
        return parameters
    }
}


extension AppDelegate {
    private func registerTiktokLogin(_ binaryMessenger: FlutterBinaryMessenger) {
        let channel = FlutterMethodChannel(name: "channel:tiktokLogin", binaryMessenger: binaryMessenger)
        channel.setMethodCallHandler {[weak self] call, result in
            switch call.method {
            case "login" :
                self?.tiktokLoginResult = result
                let authRequest = TikTokAuthRequest(scopes: ["user.info.basic"], redirectURI: redirectUrl)
                let result = authRequest.send { response in
                    print("Auth response: \(response)")
                    guard let authResponse = response as? TikTokAuthResponse else { return }
                    if authResponse.errorCode == .noError {
                        if let code = authResponse.authCode {
                            print("Auth code: \(code)")
                            var value: [String: Any] = [:]
                            value["success"] = true
                            value["result"] = code
                            result(value)
                        }
                    } else {
                        print("Authorization Failed!  Error: \(authResponse.error ?? "") Error Description: \(authResponse.errorDescription ?? "")")
                        var value: [String: Any] = [:]
                        value["success"] = false
                        value["error"] = authResponse.errorDescription
                        result(value)
                    }
                }
                print("result:\(result)")
            case "share":
                if let dict:[String: String] = call.arguments as? [String : String], let shareLink = dict["shareLink"] {
                    let shareRequest = TikTokShareRequest(localIdentifiers: [shareLink], mediaType: TikTokShareMediaType.image, redirectURI: redirectUrl)
                
                    shareRequest.send { response in
                        guard let shareResponse = response as? TikTokShareResponse else { return }
                        if shareResponse.errorCode == .noError {
                            print("Share succeeded!")
                        } else {
                            print("Share Failed! Error Code: \(shareResponse.errorCode.rawValue) Error Message: \(shareResponse.errorDescription ?? "") Share State: \(shareResponse.shareState)")
                        }
                        result("success")
                    }
                }
                
            default: break
            }
        }
    }
}
