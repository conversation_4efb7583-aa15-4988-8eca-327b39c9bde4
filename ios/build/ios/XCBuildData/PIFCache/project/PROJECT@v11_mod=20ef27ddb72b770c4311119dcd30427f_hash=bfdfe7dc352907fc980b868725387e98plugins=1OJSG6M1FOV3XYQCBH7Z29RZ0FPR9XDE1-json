{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983932bb2bf1b19579507d602837d7d3c3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources/device_info_plus/DeviceIdentifiers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846b51461bf62d511a97f07781e20c248", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources/device_info_plus/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f9f459a2b5ac5992f43959999ccfd2d0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources/device_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98035c710d9e757111b18ad781bd83c715", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources/device_info_plus/include/device_info_plus/DeviceIdentifiers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3d54b9e92a6d89db0e0c53b99469477", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources/device_info_plus/include/device_info_plus/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d39622f477051a08f9ab10b5c3cc6462", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c921d52d9d4563021e62c664710c7d82", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801687a494fe7c1bbf32a0bf4326030e2", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985322788ae6e951123300a4163241dbd9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988951b702b1ada40e7c32e96a0bb0f227", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844f41f275532a10336489fb7361f0a3b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c45f495ee0e9efec31777737315a7493", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6b9886441fec224aa2c5537ac5b6a76", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e88145b64610bf64d0345bd17ce2db5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885558f4d9493d1c3ab6ba1a8083c2335", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876d91e5839054c4908dc9361d82c08b4", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873f8a38402ca40717cda04b673b1f34d", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871df1e20bf0f869ae350bba196d5f95c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b79306f8ebd00f1a9ba5df6831b74877", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f88333ad6ad98191066f6a1fcf7b017", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984791c70fba1ee6590f59c45bd8d8cb81", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847365505eddb13054fa40bf3297820de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4885dc4254d7d0ede3866bd8c53d83b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984472deabb9c0501ed0435f49f5df51c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ffef2a3b3865b31b15f5e7393927298", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f987a7751993e57fa4c222e63f74c229", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d3d0f3cf1d58c55b318acf91f067a004", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986271b83b7fe2d3b1dc0b455d69fb7dd1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f95e8fcb5c08be8011e8beba70092286", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985479ff7019dad41001138948388a0613", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982c4605e2da642fe65ffcb389396a60e1", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d09c4e165c30ff90520a5cecca28b7d", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862bdc57672124355c24f6efc9e7ff679", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9856b3e1d33a4cfc9c92db0e012f649a56", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98908a4fb3802878d938b44fbac7fc1ed3", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9803d13c4873a0a4e87dcc4aadfb581f93", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989616d0587eb28fa8d477880149611de8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986caac6654e22147da3ef2555b70d0303", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98616474a71bd2913e03539e82d2efcdd4", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879aad10171a08e26f77d65a8351bd2c2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9839de4098d40084acf7ceca6800d16f77", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9830f9ede7ccf5fa9cdeb8a3bde1e9a293", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982ab318363a2526934d39e25c9ee18378", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98daad087a598d41ddfb0163b8b67f6e10", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9801a26a83ae4f8f25885cd0ff1cadfa1e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98252ab0634e04622ba4ebe71ba5f437c3", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877ffe02195407f05cdc3a45a3af1b80d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eac7c3f3478dca23013807fe3a82deea", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ae3db0674e4263b913fcfe3d59d8486", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ea63f96b2f6129f2aa5704d2b40acec", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b630a0fff548908c17d4ba310b31584", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866611c77575a3c571207bb818677ffdb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a072e98603e2fa700b2461203a00e94", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879e39c521dc48ecbb2407c4b507d8acf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980af3cc5dac7ca27730d9df348ece3137", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896529035a57c82f490773636c2c524ec", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843317f13da9a40cbf1ea5d4fdd2e90ff", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be80ec7bb924fd7b0a8366c59e95e34d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987eac7725fa1d7333257051f5ac664b75", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875a127622831fc68aeeb8ef47500f8d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a95ce3907b8e5dd7a0f7534ffff194a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fae3af57e9852dac0c5ebdc903838f2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af2035aabb476307c4e792a4ead3bee4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ea3e0827c738f293c041aa174ab3eaa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef39b20b6542590353aa0c1c7981f290", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849f1c6740978c631393d690d38a8538c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fef506b5e765902d9c2658f456bcbf4c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c526885eec785a2c39422be6d903093", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d763c88549f6155a13cc95a40b97a280", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e329081eaee612d73eda692a69062d9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b056748a3a19a9356de968431a93b43", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ffd7544d839988318735d82da1aaae03", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841d9e50e9f5ef972aed1e60c71d6a5a1", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f3d2cfc79210dcd53beeb19b76e8db0", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2fa101fb5017e00d7b71ea23e12ca9e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98257780655b63b642471fec62976f1dba", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98038e148d629da4f86dd3f1ec737e9121", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879c55c8ea47bd18c43f0cab6999dc56d", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b022c33794a8653b6797416332c7998d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa888b086acdf360997008b136d80911", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfb7d6b488932ba7e9526e872819a908", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb497b2a48702e7e154705a2a84aea09", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98082bd0de7a94303d6c87fb5f000577d9", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edffe0593ccb8c9a66ba0f2f1cb96a35", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9ba541af7a1899c67e86e4286e391e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f35a101a56058f8e4ae07eecfc1eb2eb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c51cd3177cbdc436f8bd789a9122667", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982df096c4783d383ad628588a14dc7359", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986886007803613b85cdaf846e93da7eb9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc59501f915bce897ab4fb2a884c613e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a856c361e745d08f7aa257d3a0fda832", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983f7d242a6a5b5586c5fc5a2a927b05d4", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985b1a51de5ef158a8f8037176bce2d861", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98933571e32a874cd272513cf0744e3038", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ca8d62780069510e6672a624ccd3171c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98538b7f1d815b412904138110da9649b2", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851af596e003ebeafcef0b6caa8fb8c8d", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4320829e06875ff089f8cd084d2a35c", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e38ab6467fd4cd5a7a261cdab00964f3", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988f9b2b47d3bb2f542f094a18345dcd76", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838d79946ab5fedd1c7853346a736c349", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988da6125d7274d28e0529d5195020fe89", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984bceded584ea155135254944349acb23", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981618ab2ac7420c3e16ffaa51aedeaa7f", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dca9bf4e22f2303d2b86dc9c0d1a832", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e97487e7b7b9a5e4e6b3d092ed29bc0d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98486e9944d74fc17303404149b20413dd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9860dc4070aee72a3b62c6887fc58f8ce3", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f881654dab09f1a0d15a740017433e2a", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c6b96b403d4ce187fd493c1612c65b", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1e64e63a8f2f4f3cb408c065cd5763", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7e2b168c7b4e9d2f142dee00bce089f", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874f808743bae0f93a0929d4d751c18de", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4ddfdac1c79f40ff4fa29dd278207d5", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98978ceec4f0f0ca98bd4a191cbe9bdd0c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98602349d2295b5f81be13b15335aece33", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874a195a5366078d63b9d74baff621305", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987765c7cd157068c5cdfa5b1ad9667840", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a60f5e39952fb23dc60e4758da41fe9f", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98138beb25bfc42f5a31127fc777349224", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e6095c5144249d664c741d532e0c848", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4de96bdd5dca5ef834b636280fde2fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d7dfacfbfbb37d38fa8a48c23cb5f63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1cb1dff4534b5f0754b47b80c7e4b98", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816be1178e60c737d210dfc2ee9061934", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98296370082af7c6c4eb6ad4b168760376", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859a57432e52326e246c97864e0fc44c9", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98880a5ead874df121ef28b48190394142", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984663f009e0f9a6b44b055202a177d7bb", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a30c429de28f7c3448739ebd20a2a084", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981c36cf89a44c36c2058c942d4bc0ffa1", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6bbeb25bbef86b73d2a66a7332898e0", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98262684ff29948bd3caa339391c4665d9", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5ef835534070b68efa6c118d67be16b", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1e151804f521b1524628f1ced6a27ac", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98624bbc54cf4900478c50a97f2f2226ad", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cc2a14c82e3f49b2c0fa5e227ab33f01", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9815815c76f2254477aa4cbac75e9fc402", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd21fb2ea549dd4f101df5e325d8ee2c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db90dd569f2ac14d65c52c4ae541d6d0", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98055338dd77929192160449f22fcd0be9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5d35c01af9a5ff1d5d22a77f188f234", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856ac25c6d72ef6e3175ef57061573b41", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3aa94bebfebfa875e5e0404dc01ae93", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4ee044d62a88f1be7d8e5497cb1286c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc1dcf9b8ae25574952f0be8eea53c92", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873ebf038cd85825645ca141b23f3c255", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847e0c85c8f46537faf097b7baa2e8a4a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860b4c5a418fc18a8ad2b959de3b2da90", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb5308cd22753459489de4727c55b1f6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c04a8345e2057b6bb53b41090211212", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb63e6e62e910405ff600529c980036e", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845190e6bfa0df3b55ad6822d00a3e5c6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eed7883f09b9f2e7e8d1e6866ffd4987", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9888a5afe20393eb93ca9658595dd75235", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988869d765c51c2de90537e34d48bc06eb", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b9fde6e963cbe7089f80d4652fc8191", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98861661afb12f6871c75f3892fccc6e53", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0031ec55c898a989c766980b688fdf4", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98beedee87322a68f0279af267482f022d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b3b4f7469315cbc714ed1ea46827faa", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aeb4e3706096e76bbfee73269f34e02f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca6f73b2f17b1ec0857affc4f91fb573", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987abb23e135b54f21af45e0d2b72c0c56", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d14e9fac508881c17433b9448daaba8b", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ac3f293574ad432979b2ab2b6f00f16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989de8bef31590fcd2d1d023416eaf2df2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed1de7daa125fb268c15243cb01949e1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d60da9ce30353b9c2e99b7d3a1f4afbd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980267d1cc0f7600719b66d9f41fdbf602", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be2f8827191b72dc5dd8eee2e4ecfdfa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879ef8ac3b2da010c6500b9e318dd500e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986268635f9218adf23b4b089dd51b4194", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9843fb36aa8547877345f63f542218f839", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98928029dc9b5b0e84df8ad28ba9192d45", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e250c52c77cf2303b52e822bc5ff1735", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98445261f469ba794f2f36660ecd8313d3", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889d3c58c8eaf55fa451b1e5444b28406", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9836e5dc7f73d526e1707a90ba02f23c86", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848fdd0a7ca0dda21ecb60d9d615967e5", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987306b30a1eb73aca094b397131e5b139", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981cdc9b0b9f57bd775ea90a59e7c2770f", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b8e12d7ab5f83a23c4fcff41359c3702", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d360708d2604f6ec15d138b14036b848", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0bc165b3ebff667076053861ecfe454", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de341a37baeda04b197dbf5a3a8d5567", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982850b5630c00ca09dac8e22740730e77", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d30bb90733c737c857581d2112fa02bb", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbebd91af309de88026910a33652a521", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848ccef021b7acdac3937dc49dfa80a6c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98311ed01efe93c48d7e421139fae6d026", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831e40a7f67ea90edcfa2aa3c9985ae2a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827b1d88090fd3798f7b74a5dd61f11fa", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861ec6d9bb80cdaa22661159bdc51139c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb2d0f825597e2c14db91b609a37aae4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cf8a614b5c5822fb8213aacf4ff6f8c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9ac2a17d21d42f2439fcc402fba10e5", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa8a32df436663744ee19ce0b817727b", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3c011b5a8a18ff53832ee4856ed44fa", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bfa95e472ec32e27d1c4c8e72f2ca990", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b68d9d10bb36cccbe65360635165af8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989f39f84f62a8bd5676fa6375bc3e0d20", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce7e91fdab2881b4b6daebd0450ccaea", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe1494d44af5a21cbcbe1526a0dce46c", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ee9bf508f2c54cdf43bc77edc00772d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c0191412579b072ca651a2caf00d618", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98380fcf51163535a047a705785b8b965b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f7da55f7ccd6ebd91b04084ebeb0387", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a08d7f1face9ec8a94b9e2288c78ee38", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f30e89f840727731c3c9d78a48b58c13", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854e66221a2ad9183569a6852e61f8415", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b33b962c1ba14fb693e4a8b2eabe05f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e478cc162fbdb82d73fc2007bbe4caee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857bd6d751465140f12690cfa5c96077c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ac6c56d2790aeac44c762b24091bc20", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c947eb4708c005c77fe491b7d14ee44f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d211a63ce2f1d4b705c77d41e8693c14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd517f9d39c8a7ffd45b26bb9d6d6c6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6e1522d9b151183186bbb45ae828814", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98db680035967c9f39620022c4b58116fd", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98535aa81d2a128ee15657434dc281d425", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ff3bb08ba56488d86f560833d150b120", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9807ec53e024e0ba8440d143c4ed67df99", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a7ca1560c533fab7cdf7e4eaa70ff6c1", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986733526b7471af2bcabfa47b7bd898a6", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982e57cfce11c73343a082d374000a891f", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fcdf1eda6fb89e6b24374c20998fb79", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987885cd127c66cb54b1c213dd24c27555", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f9c0614df1a2e2080fe09b75d97f648c", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981fe743539b80ce490e34d1ac7c925161", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982eede3bf5d393825951e0ce543cd06d8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce4ba9579a2fae2d76e246df651fce66", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c97cb00c26f8b0c47333a03edbce8baa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983a3adec69bc7e2d1b78bf9f03f6b2475", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f35181460037b3c7641a06c91babe6a3", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cccf4490aadc7c1bcd9d2deb6577c90", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822675460f5de31ca132a1b941a161056", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1f8f246bd2ed300c2e986d63153f4af", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bda07da7b9f8daf934475d3b1a04bfe5", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df1346c6ffd893f81c8d440f0a58fa27", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98191eb127fe8ad3cc6ee488d767e20d0b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845b1a909371ea3362a31c0af94347614", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812c8efcc4ec3590954da169cd0c7d32a", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820c3391c52aeaf6e47ad9487c159f61a", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821e14371002be6b5a0c9a45e068fafa4", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1c942b89da31ecc515033abecaa07e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b08fa3de8304e4013f6fd4246236a815", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d7eafd0e078e1d44da7e823931b4dc2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98985fe5ed1956cb444cb49a96cee682bd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b205702304b22d03d8523035a94764f7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd3aa9413d2eaf2d203b00101da1b944", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e24ff773fa1c0efee6188ea170bd8ea", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98053428ab010559de027403aa26f672de", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98140e2d446e9900b3713e4b941cf6c017", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6ba655b849f309357160632e150dbca", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cacbe638f7c2b7bea2a0095316767a1e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a74ee296caf58d2267d86976b300229a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bedc9e118c8800a274d199bf7e63a5c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850aab026ddaf72c101576417acd07b6f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981160417112446c514a77e6db924e6121", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1901b7541af99c3f9737e67cf8fb2df", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bae22e4699c4b029e21b05c39a8c2a6f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0568ec7028dfb330e32074bbb06f48a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e09f395fa6efd7ed79fb228b629e0563", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edf789adeb3040640d4cf9fce267b5c7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982399f205cb57994597ce3a95704ee1de", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ba83fc046daabac59ee1b309c01e69ca", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883aab394ab77f0ebeab6210b8af44e60", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b176f19c6561c75dec83f1d271cb6c2", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819729bce79c907d5ce97c8fb563627ef", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a1ee72bd112854193d65df3821d8f7e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985103d76aed2b7a8c6efba9b9de928162", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c7cd86e671fd3ad199a0ac7cad6db84", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988561d37aec051b2c7c8149f01dbac5a3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884155483bbaa31eab7e98486c811e823", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caeaa841454ab147b3f859d47175b940", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ea604e6272a29c2dafd83ebdc280d21", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98529a650a49af8bb996daf441beebceeb", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b5841af500ca326446a56296f84c29d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e235986a8bc45b1f04a516d5089edc57", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840a88d1f4dda4ca09a9b22bdd6786fa9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859a7192d94223e3aacfd4043679908e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987223f497637720bb9ec7e69cbe7eb7e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c89f047e54e4bee1552847ee9bd7c586", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834aeead89022be2ee679a1b40aa1af7a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982607661c53a0e222cb01616c62aedff0", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98747902642039d0117384bda66ce10f6f", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e98dbd7ce636ab8c27b2f8c3af7a6383f66", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98093aa60b352250ab9316243a5739fc3a", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984505c01900c842f10d3625829a5b85c2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e453a8ff624121c8da676b97b2d34678", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f5ebc4bcec8814fb0444273427ad747d", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98132443bee86db32a0ebcaf6c0b04f3c8", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9893e40dddef8f6dfb47a07c713cf4dd39", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e88b2bdefab76fd77d3df1384a957bfb", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5b68f30524f6ac578bb59473845eba3", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e78865bcdfe8d5c1d1c8f0048a7f11f9", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9826f7b9354dfa9309440a6d49af38e237", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98510cb72bb0e5f73498225f2cfccba42b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854a1b2ad4c5f62560e1973c6d362edf1", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982549e69ae63894eb4e1f68ea56a22e3d", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/tiktok_sdk_v2-0.0.3/ios/Classes/TiktokSdkV2Plugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f97b177a8c162a2883e50baa4d6212d1", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98324f72cd5654b9838603712f2d9e1cef", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989590882684f21b3386186810675f40ad", "name": "tiktok_sdk_v2", "path": "tiktok_sdk_v2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989eca11319f270b07d4efdecb25d81a8b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abb515777a99bc501297f073c07e0e36", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1a347eb100cf0d96788be19b958fdb7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984679b988dfb2f5079ef509deeb90e9a4", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98718868cc3e567355d1dfe9cb3353ca18", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2ae0e6af8f46b24358581e9e0e250e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c2890fb3425bb4dd49ad6e9e2ec0d6d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98611b3d6e6cddecfcc538e1c724ad38a3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98006a694846caa5e04957b8caa7250a5b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0e5eb389899f7a3514e985ab4edb967", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983faf19211d43abed915a27ba9995b6c7", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/tiktok_sdk_v2-0.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981d0288dc53706b72f11a80a76a8ac2e9", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/tiktok_sdk_v2-0.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9895e523eb5323f02fc26c871e91626dc3", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/tiktok_sdk_v2-0.0.3/ios/tiktok_sdk_v2.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9897afdb6288026310766abac2f38eb2f3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ffb191b83b1edadc041ddcfff0ef49af", "path": "tiktok_sdk_v2.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884ddcc0ffcd98907a74044e6aec0f4b4", "path": "tiktok_sdk_v2-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9803005fae3567f9edaf960ad8fcc0130c", "path": "tiktok_sdk_v2-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815eda9c63efd1e38da7dee669af20702", "path": "tiktok_sdk_v2-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4c09f681db9ee33adab36c309cfa75c", "path": "tiktok_sdk_v2-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e96c7b42bd6a9886c9d57ef674884b15", "path": "tiktok_sdk_v2.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9885012f3ceab1473c2b6fa52198b3b0cc", "path": "tiktok_sdk_v2.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cfa665d2fc900f1e828226ece662db3f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/tiktok_sdk_v2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98992b5223e21fd6309ff30f6f5f0533cf", "name": "tiktok_sdk_v2", "path": "../.symlinks/plugins/tiktok_sdk_v2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98efbeaba56b8c1ccd4938b40117905926", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a559b7b78aa812760b7a4edee94c117a", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98908cca8c99b4a090ad19b67886f8b02a", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98177f00562049499a54ee57a666b74699", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ab8f4e492bc7b6cff2f741371c692fe", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2396b8411887d5b5e713fd2a00ad74b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98528df23130aa8783c953355d286e6973", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2b545b4ec89a7bfeaabfe919e7c9094", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98141a8cd5f4a9eec5e535a25acb0246c3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff5ad73bbf60f30d565a75e47e1293b9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d96025e638af29481ad98612dc48059d", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987422687ffd85c54eeaf352927c0904f4", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987254089e734ec128546e26692d2c8a96", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e3e1316c376704e335abb805fa997848", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9841e5dd7f08bdc11fac9c798ca4dff595", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f9c6cc1ec2b42a0184346853c21ad8a4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d4e0ac95e550478b91360892e17b824", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9838ed4bd6e322253906c0792a07ce6d65", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9d48c81a007132411e83c42e28d99a1", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809db6f6d9dbdda9660922c4ca8a06aed", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b50b573982cdaa0596a0915b91803920", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988932cb5fb203d08c44495d79a68405ff", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f755917722c27e2b3207811c8251cba", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4796f5e60da69701ea2ca7ff583e372", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c0ed05c297bf1cd2987cd806b94277e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1f5e9d47d01844b0344e443731531e5", "name": "milestone", "path": "milestone", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830dcd04f9aac23dbd12ea942eb69538d", "name": "codes", "path": "codes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e60d0b9c6fdb9828cfe5429abaafdea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874d78d9b637de79bcc6f3b410ad4f51b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867940d93583b6fd4e32fe1bd4b1c146d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980dbb702947da58e8b26b37f83457c20e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e33a23fcbd29e50c38db0c108405e38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989aa24c697c0e36b862d4e8a0a519d9fe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98096613346696d9c6b43e797a51233937", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cbcc280006892c6b437ce5a28ce0d33", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9842e54772a4ef66da2bc7a3134aace488", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983b92025ecfde22289014233d2b0999e4", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a18391c702f13aeaaf15aa211e61acb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984b0d9a4e1fab5495f9ed073c9317cc01", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b75fd237a7ded7ffe9b5c013cd13f72e", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d361874a198d4fd7f8f43c2647b2de7c", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9801cfaf4f9d2cf29d3103c017307080e3", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98913eb3159335366809b957c4160cbeaa", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f6521115c344c3992dc8cc3b10bfe12", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9894f04f1c738b59e475291f164bc57c1d", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d7614490f90e28c9c2568b467cc94d58", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dbb4d2692f65d0fc8d6f87ce55f1d4f8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849ab9ad50e8d6dd50a426de912aef0ab", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc7916e97cda167af72704401b1d6867", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98172ba60474b8efc20038a09ae209cbe4", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98c4bcb52b6ef922983b347a324d1d3070", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.0.sdk/System/Library/Frameworks/WebKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987df15d7af16539aac9636ec0711c48be", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980db8d57e471489ba790f1078d8cdd411", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985450aafbac54ecf7ba886563715cebc5", "path": "Sources/TikTokOpenAuthSDK/Public/PKCE.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982ba2870a6f42794765d2ce7fe0800507", "path": "Sources/TikTokOpenAuthSDK/Public/TikTokAuthRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9885b807c1eb9ffe3d0d4af1c9e84a456f", "path": "Sources/TikTokOpenAuthSDK/Public/TikTokAuthRequest+URLParams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db61a6aeb689fed577f56f3003c0658b", "path": "Sources/TikTokOpenAuthSDK/Public/TikTokAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a8328620de7317d164a81158d886eb6f", "path": "Sources/TikTokOpenAuthSDK/Service/TikTokAuthService.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a35aa6c75879ce7e33f09ac49ec2284f", "path": "Sources/TikTokOpenSDKCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98457f669d04e552934e1bd2c0209282c9", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989114274aafbd3d09acb9edc003488f68", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98be9641ed41ead91818e14715dfc9a135", "path": "ResourceBundle-TikTokOpenAuthSDKPrivacyInfo-TikTokOpenAuthSDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ddd3684bc98d1c6ec95891a065c1d169", "path": "TikTokOpenAuthSDK.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823027a1464536ac15b8999fd86c28938", "path": "TikTokOpenAuthSDK-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982deeed461b4abf42b57e7b0bab3e5cb9", "path": "TikTokOpenAuthSDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f073f64d83c84e79c083acfc7339099", "path": "TikTokOpenAuthSDK-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98687ee2beb63d62300d0f0710676d1186", "path": "TikTokOpenAuthSDK-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9874235fe094b557c7afbb8c51e6ddc10b", "path": "TikTokOpenAuthSDK.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f5d2f19d22c3cf3119b225c95b15095f", "path": "TikTokOpenAuthSDK.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cbee64f44cd6140cddc28f04d29dba0c", "name": "Support Files", "path": "../Target Support Files/TikTokOpenAuthSDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98352181781add64102c0cbdd73220a089", "name": "TikTokOpenAuthSDK", "path": "TikTokOpenAuthSDK", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9856efb39be7f6a8b494c9e9c8d7a7e974", "path": "Sources/TikTokOpenSDKCore/Service/TikTokAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988014fe293c3e2d24ad6bfd34917f5800", "path": "Sources/TikTokOpenSDKCore/Internal/TikTokBase.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e3e46f2eaaedc5616c07a6fbed2ab159", "path": "Sources/TikTokOpenSDKCore/Utils/TikTokInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982df9d57ccec6bf2cfd3af5afd9667fdb", "path": "Sources/TikTokOpenSDKCore/Internal/TikTokRequestHandling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d618173a2fb852fc23a77c8e652dbb61", "path": "Sources/TikTokOpenSDKCore/Internal/TikTokResponseHandling.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb12788e924f76d00923336eada8c7dd", "path": "Sources/TikTokOpenSDKCore/Utils/TikTokRSA.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1c0b1cac2d189c2ed574c5e9949168c", "path": "Sources/TikTokOpenSDKCore/Public/TikTokURLHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810bd5a082ec147a321e729803e283a64", "path": "Sources/TikTokOpenSDKCore/Internal/TikTokURLOpener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b67dedad5a17f6ef55783d4d4c3ed8f2", "path": "Sources/TikTokOpenSDKCore/Internal/TikTokURLQueryParamsConvertible.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981c395ba11f68f21ab2d38b66a523ea2d", "path": "Sources/TikTokOpenSDKCore/Utils/TikTokUtils.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9871c3120471ae7b134bfddcf21cce8916", "path": "Sources/TikTokOpenSDKCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836337153a63db4375f07d3cd263ea697", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acf4dbee85181188cf029ce8b6655fb7", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cd6f10c62052230cec199c3f6c7073b6", "path": "ResourceBundle-TikTokOpenSDKCorePrivacyInfo-TikTokOpenSDKCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980638a780dcea5b748a868f0a211805d5", "path": "TikTokOpenSDKCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f514052e802d807a4cc9a6b86e2b937", "path": "TikTokOpenSDKCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ab0b7d0043d1427b8bd9c3d15cb1250a", "path": "TikTokOpenSDKCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98655f3a6daaaecb08c34125cd36bbbdfe", "path": "TikTokOpenSDKCore-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d2602786b69376503741d8496792aa2b", "path": "TikTokOpenSDKCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800dfefd0b8691d1430f7c1a199162dfd", "path": "TikTokOpenSDKCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cd2ae5f79872b52f3852cfa083893b7b", "path": "TikTokOpenSDKCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c9e63ae28781f3f4976de345c34fc8e", "name": "Support Files", "path": "../Target Support Files/TikTokOpenSDKCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a45896ac6d6974cc0484a17f14038238", "name": "TikTokOpenSDKCore", "path": "TikTokOpenSDKCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9846c72e21fa486b39206a85de47d6bc70", "path": "Sources/TikTokOpenShareSDK/Service/TikTokAppStoreModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808d74a79851b11e9b97b4d5f9334cf54", "path": "Sources/TikTokOpenShareSDK/Public/TikTokShareRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7828e066402c0fa1fad486b35ff01b7", "path": "Sources/TikTokOpenShareSDK/Public/TikTokShareRequest+URLParams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ad57b916b7dc1571a0c2c2df134a27c3", "path": "Sources/TikTokOpenShareSDK/Public/TikTokShareResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9843d4d546d1059d99e9e6b894f64fbb59", "path": "Sources/TikTokOpenShareSDK/Service/TikTokShareService.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c9a05155bd52375dd240750d7e6f5122", "path": "Sources/TikTokOpenSDKCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98048dfbdb7f915c7067130498764c0652", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fcde709ef4c0bd8d06fb43305ae613a", "name": "Share", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e07344ccc7414bb1db043c4ae500b1a8", "path": "ResourceBundle-TikTokOpenShareSDKPrivacyInfo-TikTokOpenShareSDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988f4129e3e82e060dc6aa8387c7c8b3ee", "path": "TikTokOpenShareSDK.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98596dba2da477d2e8073f2643eaa9d875", "path": "TikTokOpenShareSDK-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb808b4e423da32154b502a76e4c62ce", "path": "TikTokOpenShareSDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ebbade80499c020446474aa0cf04536", "path": "TikTokOpenShareSDK-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fa05897cb59e8e8ef7df3d22d5d4bec", "path": "TikTokOpenShareSDK-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98473fd985496be4d7f58dbf3314cacc11", "path": "TikTokOpenShareSDK.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988bfeb09a8db6457c5f2282dedd020cfb", "path": "TikTokOpenShareSDK.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98184c9b4d15320ea915b76daa01b5b091", "name": "Support Files", "path": "../Target Support Files/TikTokOpenShareSDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfb533ef2231a49c48a8ed584aed2e0f", "name": "TikTokOpenShareSDK", "path": "TikTokOpenShareSDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8f8ff3985b52e1faf95cc4089500f4a", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98ab6671b8f9c66469cb5097142004633e", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d85490c13bb594476aa9be285597497d", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c50a2eb9fb28cebb3540daef5d4a8334", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b0bdfb96c434b1bdaf98ff08db5d964", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c24b47fd1a04f7c3870243d256eb710", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e986b56855213c29113cc17d2b495b4605b", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98850ee204ad70211ee248c6855349a5f9", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f67184b23266d4586865ab49f1bd9d8e", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986175ec003efd0925b0e80b27c1a333bb", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c04a92782605bedf8b5bd015c8dc01a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab5e5b11fcf9a2f9f4c2bf61b3a5e465", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2407b6e3245e78631c8d5833a16aaa", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983aadeb6c0efc55aa61d4a193c33d1a65", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a634232c699d5ed3646d3f024c937ffa", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989938b906e3cb2707a2afa9a39150a604", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d01c1d667722a31ce8e51428338963f", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a85576fdb8cb73c7cd4dd5902a45a27b", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f448039d0a832e98acdd3ffd87da1731", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98074a9b441078beef16a37aae33ee2900", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bd2a5c12d5a72dad789e04e26dc5a25", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc46d149385d28a69f8f8bc860e81763", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5836cf4d97a0c9c99eca09bf2351047", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d357a58233f32f97cf5aa060ebc8be", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/codes/milestone/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/codes/milestone/ios/Pods", "targets": ["TARGET@v11_hash=6bcdc5190b8747fc987ab98ee2512de4", "TARGET@v11_hash=dd748476fe2f5c25be6b862c4dd9d92c", "TARGET@v11_hash=a9d9a542bf9955b32348e42642ed3268", "TARGET@v11_hash=875dc362aadf8327a72d62ac13687d83", "TARGET@v11_hash=7a3dc42891bdf29075ab599928392095", "TARGET@v11_hash=8beb7716f860f0af8cdb97ba03790097", "TARGET@v11_hash=1108ef9ec074c3fdc5ec6bdf6709da52", "TARGET@v11_hash=a636083479b0f0fdaad55d27d1dbe990", "TARGET@v11_hash=f44e08d129b2c2b2b7edc435e7f0d95f", "TARGET@v11_hash=79588e22627d1c1c0bf2ac47c461e6c2", "TARGET@v11_hash=9bde4ff9c851864f407e7e3be710d3bd", "TARGET@v11_hash=a064749182203b29610e89abd5954b70", "TARGET@v11_hash=c784c75a518fc155d72315b2fc395349", "TARGET@v11_hash=a4d9bb02c6449e68bf7256768ad3a6c5", "TARGET@v11_hash=cdaf2e50d082da7d926011dc6e8c011f", "TARGET@v11_hash=8224b9e557ff553e5c875f60591811e7", "TARGET@v11_hash=d6f82b85c558be02ba1e28f83df90329", "TARGET@v11_hash=827e035e7cb3bafa7eda28a93709ce85", "TARGET@v11_hash=c0f4118d32ee03da538b526cc5bfdeed", "TARGET@v11_hash=cc810e247a54a3b7d53eeec15760349c", "TARGET@v11_hash=e271152c3d5fe0e25e3d5d0df8f49576", "TARGET@v11_hash=bb6e94bc77c5cacf5e228359be360c18", "TARGET@v11_hash=f3823a716f29eb9a6be5118cfb4af9bf", "TARGET@v11_hash=9de5cc24f8d62074dffa4bd60e00d8b2"]}