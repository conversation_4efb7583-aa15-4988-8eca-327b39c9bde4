{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874235fe094b557c7afbb8c51e6ddc10b", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Development", "CODE_SIGN_STYLE": "Automatic", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenAuthSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenAuthSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/ResourceBundle-TikTokOpenAuthSDKPrivacyInfo-TikTokOpenAuthSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TikTokOpenAuthSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981c9e76d8b8f05d8895653d32b58d2b90", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5d2f19d22c3cf3119b225c95b15095f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenAuthSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenAuthSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/ResourceBundle-TikTokOpenAuthSDKPrivacyInfo-TikTokOpenAuthSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "TikTokOpenAuthSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98666ee95d0b73f3e6e37031189250b6ef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5d2f19d22c3cf3119b225c95b15095f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenAuthSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenAuthSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenAuthSDK/ResourceBundle-TikTokOpenAuthSDKPrivacyInfo-TikTokOpenAuthSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "TikTokOpenAuthSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9874328f20f5675b831cbfb408171bb602", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9880a70e3c0e21b37b5ba89f77d9a694aa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98307eb0a9142ff181db4376540b5e9b81", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a35aa6c75879ce7e33f09ac49ec2284f", "guid": "bfdfe7dc352907fc980b868725387e98f546943a602e39ac5ea1103649c7f003"}], "guid": "bfdfe7dc352907fc980b868725387e984d0640827db54b2d1e8fe2d615be63ec", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98081d572d9d583a917edaee208b416672", "name": "TikTokOpenAuthSDK-TikTokOpenAuthSDKPrivacyInfo", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f29d3bba7c8b12f3ea1d0d8cca84818c", "name": "TikTokOpenAuthSDKPrivacyInfo.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}