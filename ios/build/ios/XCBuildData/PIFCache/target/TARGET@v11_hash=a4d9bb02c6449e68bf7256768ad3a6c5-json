{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e78865bcdfe8d5c1d1c8f0048a7f11f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f292fa1a2e602e72cbae03ca4381d0ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9826f7b9354dfa9309440a6d49af38e237", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c2c7bb83ef85df2b5738875e12a3b9e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9826f7b9354dfa9309440a6d49af38e237", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.1/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802786e96202a989faf8737401d76d959", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c5b68f30524f6ac578bb59473845eba3", "guid": "bfdfe7dc352907fc980b868725387e98e37596068807202609d79cb2ba55aed5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c942b89da31ecc515033abecaa07e8", "guid": "bfdfe7dc352907fc980b868725387e980d97e13e581466eddab24895c0f38121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d7eafd0e078e1d44da7e823931b4dc2", "guid": "bfdfe7dc352907fc980b868725387e98058f34a6f5f9ab7bed1da5bc97f3eea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b205702304b22d03d8523035a94764f7", "guid": "bfdfe7dc352907fc980b868725387e98b2929fd22557df8a8f1c2fb6c28aea1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e24ff773fa1c0efee6188ea170bd8ea", "guid": "bfdfe7dc352907fc980b868725387e989baa591b9cf4c12574e844267ae724c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98140e2d446e9900b3713e4b941cf6c017", "guid": "bfdfe7dc352907fc980b868725387e9859ca486413235e042525305a2c06e5e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ba655b849f309357160632e150dbca", "guid": "bfdfe7dc352907fc980b868725387e980a5f6a29af70e0a3a51b5c3729f84231"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cacbe638f7c2b7bea2a0095316767a1e", "guid": "bfdfe7dc352907fc980b868725387e985b66854061475aecea834ea6c805ee2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bedc9e118c8800a274d199bf7e63a5c", "guid": "bfdfe7dc352907fc980b868725387e9814986a52b6e4f83d25cc02b2c77e2990"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981160417112446c514a77e6db924e6121", "guid": "bfdfe7dc352907fc980b868725387e989023df7b70519701187dd4bb7b86883c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf789adeb3040640d4cf9fce267b5c7", "guid": "bfdfe7dc352907fc980b868725387e98e09419f3a83e55ae061fd58dd9662afe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1901b7541af99c3f9737e67cf8fb2df", "guid": "bfdfe7dc352907fc980b868725387e989b314f2b568529078c71e97431360049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0568ec7028dfb330e32074bbb06f48a", "guid": "bfdfe7dc352907fc980b868725387e98d11ca919383f0a4b852b95989e8a40bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982399f205cb57994597ce3a95704ee1de", "guid": "bfdfe7dc352907fc980b868725387e989e36c4ac1817c290d085961a1f657061", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9732a048f02ac03affb4b4fa7b710bf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98132443bee86db32a0ebcaf6c0b04f3c8", "guid": "bfdfe7dc352907fc980b868725387e98db6db7db22361c5e7722392e4da662e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08fa3de8304e4013f6fd4246236a815", "guid": "bfdfe7dc352907fc980b868725387e987b2f464cd52111fdc7593e6849de5de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98985fe5ed1956cb444cb49a96cee682bd", "guid": "bfdfe7dc352907fc980b868725387e986be2e290e7351f533f62a55d12240d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3aa9413d2eaf2d203b00101da1b944", "guid": "bfdfe7dc352907fc980b868725387e985bd3e191754f8d7e1bf7bdf0d2342754"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053428ab010559de027403aa26f672de", "guid": "bfdfe7dc352907fc980b868725387e98d5d4b72c3dc876a0055949e9fb8bd9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a74ee296caf58d2267d86976b300229a", "guid": "bfdfe7dc352907fc980b868725387e98fc98380624cbb2c989a1e5561d056e1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850aab026ddaf72c101576417acd07b6f", "guid": "bfdfe7dc352907fc980b868725387e98eb7997030c8a19b44f44bf9e1c2c9e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae22e4699c4b029e21b05c39a8c2a6f", "guid": "bfdfe7dc352907fc980b868725387e98452ed0d5dab0d82afc9bb372ff97bbd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09f395fa6efd7ed79fb228b629e0563", "guid": "bfdfe7dc352907fc980b868725387e9832d9e8bee1f98a1c4918588a74cf4172"}], "guid": "bfdfe7dc352907fc980b868725387e98964da8c41b24636d9d338de21fb08fe2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "guid": "bfdfe7dc352907fc980b868725387e98015c062afdb30ba698f98b6ea63a1d36"}], "guid": "bfdfe7dc352907fc980b868725387e9856f337a0270b8ff9a09062fba0a9859f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98301a0f773483e7a62c7a59d2ce137d3c", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e9877cd28b09d57fbb5f9abe0ec343ccf66", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}