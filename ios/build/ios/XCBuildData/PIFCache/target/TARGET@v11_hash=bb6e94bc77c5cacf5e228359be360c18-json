{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98473fd985496be4d7f58dbf3314cacc11", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Development", "CODE_SIGN_STYLE": "Automatic", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenShareSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenShareSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/ResourceBundle-TikTokOpenShareSDKPrivacyInfo-TikTokOpenShareSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TikTokOpenShareSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987237b367dd7fdbaa3752708495068bdb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bfeb09a8db6457c5f2282dedd020cfb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenShareSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenShareSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/ResourceBundle-TikTokOpenShareSDKPrivacyInfo-TikTokOpenShareSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "TikTokOpenShareSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98eddf5319cf1423c78692e470e3b870c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bfeb09a8db6457c5f2282dedd020cfb", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "Apple Distribution", "CODE_SIGN_STYLE": "Manual", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TikTokOpenShareSDK", "DEVELOPMENT_TEAM": "5ZT64D7ZTC", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TikTokOpenShareSDK", "INFOPLIST_FILE": "Target Support Files/TikTokOpenShareSDK/ResourceBundle-TikTokOpenShareSDKPrivacyInfo-TikTokOpenShareSDK-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "TikTokOpenShareSDKPrivacyInfo", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9821f87bd822aa6930dea9cd04aa4f2905", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b849626b6c6d991cb52a8260df780c5a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ea9eb243eed0ae16ec65a633967e83ee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9a05155bd52375dd240750d7e6f5122", "guid": "bfdfe7dc352907fc980b868725387e98778eec454116f14465b194e37fac5ed7"}], "guid": "bfdfe7dc352907fc980b868725387e9895227f343ec1604352925a826e1f7b8f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98f4278eba82c0076e23013582c0424328", "name": "TikTokOpenShareSDK-TikTokOpenShareSDKPrivacyInfo", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9807eadd85e12210787694492477053cde", "name": "TikTokOpenShareSDKPrivacyInfo.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}