{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800dfefd0b8691d1430f7c1a199162dfd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TikTokOpenSDKCore", "PRODUCT_NAME": "TikTokOpenSDKCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9d675ca7b7bf5d4a871038ede2918ff", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cd2ae5f79872b52f3852cfa083893b7b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenSDKCore", "PRODUCT_NAME": "TikTokOpenSDKCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986bad6d5bb094a5c8ab37e1fb295aefe8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cd2ae5f79872b52f3852cfa083893b7b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TikTokOpenSDKCore/TikTokOpenSDKCore.modulemap", "PRODUCT_MODULE_NAME": "TikTokOpenSDKCore", "PRODUCT_NAME": "TikTokOpenSDKCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98618dd936ddffc1dc9dfacd5de74682a2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2602786b69376503741d8496792aa2b", "guid": "bfdfe7dc352907fc980b868725387e98da254e51e3beb79414155687dfdc1dd9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98712398595a1226880cb7c6041e93ce75", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856efb39be7f6a8b494c9e9c8d7a7e974", "guid": "bfdfe7dc352907fc980b868725387e98f79f88e340ea34ffc5e7b6c0ef4a489a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988014fe293c3e2d24ad6bfd34917f5800", "guid": "bfdfe7dc352907fc980b868725387e98bcd81907b05241957e05f7145da4b8f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e46f2eaaedc5616c07a6fbed2ab159", "guid": "bfdfe7dc352907fc980b868725387e98291d7522a21a849be5ad9dcbbda0998b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f514052e802d807a4cc9a6b86e2b937", "guid": "bfdfe7dc352907fc980b868725387e9886866f67259a3f2584804cf797bf523a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df9d57ccec6bf2cfd3af5afd9667fdb", "guid": "bfdfe7dc352907fc980b868725387e982307a2356266de31ca0a02d5f3e00427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d618173a2fb852fc23a77c8e652dbb61", "guid": "bfdfe7dc352907fc980b868725387e98c99a30c4a0d1ee9786bfc6c31490929b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb12788e924f76d00923336eada8c7dd", "guid": "bfdfe7dc352907fc980b868725387e98b3754fe9cbf7b88c7b376c07fec539f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c0b1cac2d189c2ed574c5e9949168c", "guid": "bfdfe7dc352907fc980b868725387e98abff82bebd76627af314dd0ae86e6c64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810bd5a082ec147a321e729803e283a64", "guid": "bfdfe7dc352907fc980b868725387e98716819eeadd18a74fd6d6266663eecf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b67dedad5a17f6ef55783d4d4c3ed8f2", "guid": "bfdfe7dc352907fc980b868725387e983f6f884901c1b95ed573314742b90d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c395ba11f68f21ab2d38b66a523ea2d", "guid": "bfdfe7dc352907fc980b868725387e98907b13b466ea42e4ec51ae6fb6b60d96"}], "guid": "bfdfe7dc352907fc980b868725387e987d36624a33eece10dd0274d80a6ebf8f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb38708557615e4a040221b216db5a97", "guid": "bfdfe7dc352907fc980b868725387e98ec3c7863ff4a25d257d12c725678ea00"}], "guid": "bfdfe7dc352907fc980b868725387e9897cbbf35db7b70dc3c050a51f7230dde", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985f953a2ad4c858c50d0357f97368ed19", "targetReference": "bfdfe7dc352907fc980b868725387e98297782ee77adee16cbf50dee94db8fc5"}], "guid": "bfdfe7dc352907fc980b868725387e98d436b0f2a5d71dd1c55a056f80ff26f8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98297782ee77adee16cbf50dee94db8fc5", "name": "TikTokOpenSDKCore-TikTokOpenSDKCorePrivacyInfo"}], "guid": "bfdfe7dc352907fc980b868725387e987487e08017d4d7651e52f9beb45fa96b", "name": "TikTokOpenSDKCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9839cfb5bc919283ba95c72728d76aa7ce", "name": "TikTokOpenSDKCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}