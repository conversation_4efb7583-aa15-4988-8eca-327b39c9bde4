import 'package:event_bus/event_bus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/user_info.dart';

class ProfileUpdateEvent {
  final String username;
  final String? avatarUrl;
  final int? memberLevel;

  ProfileUpdateEvent({
    this.memberLevel,
    required this.username,
    this.avatarUrl,
  });

  @override
  String toString() {
    return 'ProfileUpdateEvent{username: $username}';
  }
}

class LoginEvent {
  final Map<String, dynamic> userInfo;
  LoginEvent({required this.userInfo});
}

EventBus eventBus = EventBus();

class NetworkStatusEvent {
  final bool isConnected;
  final List<ConnectivityResult> connectionTypes;
  final bool isInitialCheck;

  NetworkStatusEvent({
    required this.connectionTypes,
    required this.isConnected,
    this.isInitialCheck = false,
  });

  @override
  String toString() {
    return 'NetworkStatusEvent{isConnected: $isConnected, isInitialCheck: $isInitialCheck}';
  }
}

class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  final Connectivity _connectivity = Connectivity();

  factory NetworkService() => _instance;

  NetworkService._internal() {
    _init();
  }

  Future<void> _init() async {
    await _checkConnection(isInitialCheck: true);

    _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      _fireEvent(results, isInitialCheck: false);
    });
  }

  Future<void> _checkConnection({bool isInitialCheck = false}) async {
    try {
      final results = await _connectivity.checkConnectivity();
      _fireEvent(results, isInitialCheck: isInitialCheck);
    } catch (e) {
      _fireEvent([ConnectivityResult.none], isInitialCheck: isInitialCheck);
    }
  }

  void _fireEvent(
    List<ConnectivityResult> results, {
    required bool isInitialCheck,
  }) {
    final isConnected =
        results.isNotEmpty &&
        results.any((type) => type != ConnectivityResult.none);

    eventBus.fire(
      NetworkStatusEvent(
        isConnected: isConnected,
        connectionTypes: results,
        isInitialCheck: isInitialCheck,
      ),
    );
  }

  Future<void> manuallyCheck() async {
    await _checkConnection();
  }
}
