import 'dart:async';

import 'package:flutter/material.dart';

import '../generated/l10n.dart';
import 'event_bus_controller.dart';

/// 简洁网络刷新Mixin
///
/// 使用步骤：
/// 1. 在StatefulWidget的state中使用: with NetworkRefreshMixin
/// 2. 实现buildNetworkIndicator方法(定义断网提示UI)
/// 3. 可选覆盖onNetworkRestored方法(网络恢复时的刷新逻辑)
mixin NetworkRefreshMixin<T extends StatefulWidget> on State<T> {
  late StreamSubscription<NetworkStatusEvent> _networkSubscription;
  bool _isConnected = true;
  bool _hasInitialized = false;

  /// 是否显示网络提示
  bool get showNetworkIndicator => !_isConnected && _hasInitialized;

  /// 当前是否连接
  bool get isConnected => _isConnected;

  Widget buildNetworkIndicator(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(12),
        color: Colors.red,
        child: Row(
          children: [
            const Icon(Icons.wifi_off, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                S.of(context).network_is_not_available,
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: NetworkService().manuallyCheck,
              child: Text(S.of(context).loading_more_retry, style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  /// 可选覆盖: 当网络恢复时的回调
  Future<void> onNetworkRestored() async {}

  /// 可选覆盖: 当网络首次检测完成时的回调
  Future<void> onFirstNetworkCheck(bool isConnected) async {}

  void _handleNetworkEvent(NetworkStatusEvent event) {
    final wasConnected = _isConnected;
    _isConnected = event.isConnected;

    if (!_hasInitialized) {
      _hasInitialized = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onFirstNetworkCheck(_isConnected);
      });
    }

    if (!wasConnected && _isConnected) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onNetworkRestored();
      });
    }

    if (mounted) setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _networkSubscription = eventBus
        .on<NetworkStatusEvent>()
        .listen(_handleNetworkEvent);
  }

  @override
  void dispose() {
    _networkSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        super.build(context),
        if (showNetworkIndicator) buildNetworkIndicator(context),
      ],
    );
  }
}