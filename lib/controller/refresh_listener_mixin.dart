import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'event_bus_controller.dart';

class HomeRefreshEvent {
  final int index;
  HomeRefreshEvent(this.index);
}

mixin RefreshListenerMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    eventBus.on<HomeRefreshEvent>().listen((event) {
      Logger().d("receive refresh event");
      homeTabDoubleTapRefresh();
    });
    super.initState();
  }

  Future<void> homeTabDoubleTapRefresh();
}