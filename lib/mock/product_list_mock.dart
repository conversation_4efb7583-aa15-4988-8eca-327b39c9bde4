import 'dart:math';
import 'package:faker/faker.dart';
import 'package:milestone/models/category.dart';
import 'package:milestone/models/home_page_data.dart';
import 'package:milestone/models/product.dart';

class MockDataGenerator {
  static final Random _random = Random();
  static final List<String> _productNames = [
    '无线蓝牙耳机',
    '真皮女士手包',
    '智能手环',
    '家用扫地机器人',
    '纯棉T恤',
    '运动水壶',
    '男士香水',
    '电动牙刷',
    '便携式充电宝',
    '高清摄像头'
  ];

  static final List<String> _categories = [
    '电子产品', '服装配饰', '美妆个护', '家居日用', '食品饮料', '运动户外', '母婴玩具', '数码配件'
  ];

  static TikTokProduct generateProduct(String id) {
    final basePrice = 10.0 + _random.nextDouble() * 90; // 10-100美元
    final discountRate = _random.nextDouble() * 0.3; // 0-30%折扣
    final hasDiscount = _random.nextDouble() < 0.3; // 30%商品有折扣
    final commissionRate = 0.05 + _random.nextDouble() * 0.25; // 5%-30%佣金

    final productName = _productNames[_random.nextInt(_productNames.length)] +
        (faker.randomGenerator.boolean() ? '升级版' : '') +
        (faker.randomGenerator.boolean() ? '2023新款' : '');

    return TikTokProduct(
      id: id,
      title: productName,
      shortDescription: _generateDescription(productName),
      price: double.parse(basePrice.toStringAsFixed(2)),
      discountPrice: hasDiscount
          ? double.parse((basePrice * (1 - discountRate)).toStringAsFixed(2))
          : null,
      currency: faker.randomGenerator.element(['USD', 'GBP', 'EUR']),
      commissionRate: double.parse(commissionRate.toStringAsFixed(2)),
      maxRebateAmount: (hasDiscount
          ? basePrice * (1 - discountRate) * commissionRate
          : basePrice * commissionRate),
      mainImage: 'https://picsum.photos/300/300?random=$id',
      galleryImages: List.generate(
          3,
              (i) => 'https://picsum.photos/300/300?random=$id-$i'
      ),
      category: _categories[_random.nextInt(_categories.length)],
      tags: List.generate(
          2,
              (_) => _generateTag()
      ),
      shopId: 'shop_${faker.randomGenerator.string(6)}',
      shopName: '${faker.company.name()}旗舰店',
      shopRating: 4 + _random.nextDouble(),
      affiliateLink: 'https://tiktok.com/share/$id',
      pid: 'pid_${faker.randomGenerator.string(8)}',
      salesCount: 100 + _random.nextInt(5000),
      shareCount: 50 + _random.nextInt(2000),
      stock: 10 + _random.nextInt(500),
      status: 'active',
      createdAt: DateTime.now().subtract(Duration(days: _random.nextInt(100))),
      updatedAt: DateTime.now(),
    );
  }

  static String _generateDescription(String productName) {
    return '$productName采用${faker.randomGenerator.element(['高级材料', '创新技术', '环保设计'])}，'
        '${faker.randomGenerator.element(['完美适合您的生活方式', '满足您的各种需求', '提升生活品质'])}。';
  }

  static String _generateTag() {
    final seasons = ['春季', '夏季', '秋季', '冬季'];
    final attributes = ['新款', '促销', '爆款', '热卖'];
    return '${faker.randomGenerator.element(seasons)}${faker.randomGenerator.element(attributes)}';
  }

  static HomePageData generateHomePageData() {
    return HomePageData(
      featuredProducts: List.generate(
          10,
              (i) => generateProduct('prod_$i')
      ),
    );
  }
}