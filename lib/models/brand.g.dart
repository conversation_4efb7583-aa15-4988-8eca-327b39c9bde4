// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'brand.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BrandPageResponse _$BrandPageResponseFromJson(Map<String, dynamic> json) =>
    BrandPageResponse(
      code: _parseStringToInt(json['code']),
      msg: json['msg'] as String,
      data: BrandPageData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BrandPageResponseToJson(BrandPageResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

BrandPageData _$BrandPageDataFromJson(Map<String, dynamic> json) =>
    BrandPageData(
      total: _parseStringToInt(json['total']),
      list: (json['list'] as List<dynamic>)
          .map((e) => BrandCategoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      size: _parseStringToInt(json['size']),
      pages: _parseStringToInt(json['pages']),
      current: _parseStringToInt(json['current'])
    );

Map<String, dynamic> _$BrandPageDataToJson(BrandPageData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'size': instance.size,
      'pages': instance.pages,
      'current': instance.current,
      'list': instance.list,
    };

BrandCategoryModel _$BrandCategoryModelFromJson(Map<String, dynamic> json) =>
    BrandCategoryModel(
      id: json['id'] as String,
      sort: _parseStringToInt(json['sort']),
      name: json['name'] as String,
      image: json['image'],
      remark: json['remark'],
      brandList: (json['brandList'] as List<dynamic>)
          .map((e) => BrandModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BrandCategoryModelToJson(BrandCategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sort': instance.sort,
      'name': instance.name,
      'image': instance.image,
      'remark': instance.remark,
      'brandList': instance.brandList,
    };

BrandModel _$BrandModelFromJson(Map<String, dynamic> json) => BrandModel(
  id: json['id'] as String,
  sort: json['sort']  as String,
  name: json['name'] as String,
  imageUrl: json['imageUrl'] as String?,
  imageUrl1: json['imageUrl1'] as String?,
  bannerImageUrl: json['bannerImageUrl'] as String?,
  description: json['description'],
  createDate: json['createDate'] as String,
  updateDate: json['updateDate'] as String,
  goodsCount: json['goodsCount'] as String,
  maxCashbackRate: json['maxCashbackRate'] as String,
);

Map<String, dynamic> _$BrandModelToJson(BrandModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sort': instance.sort,
      'name': instance.name,
      'imageUrl': instance.imageUrl,
      'imageUrl1': instance.imageUrl1,
      'bannerImageUrl': instance.bannerImageUrl,
      'description': instance.description,
      'createDate': instance.createDate,
      'updateDate': instance.updateDate,
      'goodsCount': instance.goodsCount,
      'maxCashbackRate': instance.maxCashbackRate,
    };
