// 修改后的 ProductCategory 模型
class ProductCategory {
  final int id;
  final String code;
  final String name;
  final String logoUrl;
  final String? description;
  final int status;
  final int? priority;
  final double? maxCashBackRate;
  final DateTime gmtCreate;
  final DateTime gmtModified;

  ProductCategory({
    required this.id,
    required this.code,
    required this.name,
    required this.logoUrl,
    this.description,
    required this.status,
    this.priority,
    this.maxCashBackRate,
    required this.gmtCreate,
    required this.gmtModified,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    // 安全转换函数
    T? parseValue<T>(dynamic value) {
      if (value == null) return null;
      if (T == int) {
        return (int.tryParse(value.toString()) ?? value as int?) as T?;
      }
      if (T == double) {
        return (double.tryParse(value.toString()) ?? value as double?) as T?;
      }
      if (T == String) {
        return value.toString() as T?;
      }
      return value as T?;
    }

    return ProductCategory(
      id: parseValue<int>(json['id']) ?? 0,
      code: parseValue<String>(json['code']) ?? '',
      name: parseValue<String>(json['name']) ?? '',
      logoUrl: parseValue<String>(json['logoUrl']) ?? '',
      description: parseValue<String>(json['description']),
      status: parseValue<int>(json['status']) ?? 0, // 确保转为int
      priority: parseValue<int>(json['priority']),
      maxCashBackRate: parseValue<double>(json['maxCashBackRate']),
      gmtCreate: DateTime.tryParse(parseValue<String>(json['gmtCreate']) ?? '') ?? DateTime.now(),
      gmtModified: DateTime.tryParse(parseValue<String>(json['gmtModified']) ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'logoUrl': logoUrl,
      'description': description,
      'status': status,
      'priority': priority,
      'maxCashBackRate': maxCashBackRate,
      'gmtCreate': gmtCreate.toIso8601String(),
      'gmtModified': gmtModified.toIso8601String(),
    };
  }
}

// 修改后的 ProductCategoryListResponse
class ProductCategoryListResponse {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<ProductCategory> list;

  ProductCategoryListResponse({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory ProductCategoryListResponse.fromJson(Map<String, dynamic> json) {
    return ProductCategoryListResponse(
      page: (json['page'] as num?)?.toInt() ?? 1,
      limit: (json['limit'] as num?)?.toInt() ?? 20,
      totalPage: (json['totalPage'] as num?)?.toInt() ?? 1,
      total: (json['total'] as num?)?.toInt() ?? 0,
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => ProductCategory.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'totalPage': totalPage,
      'total': total,
      'list': list.map((e) => e.toJson()).toList(),
    };
  }
}