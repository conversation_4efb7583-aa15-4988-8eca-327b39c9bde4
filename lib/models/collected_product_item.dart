// models/collected_product_item.dart
class CollectedProductItem {
  final int id;
  final int productId;
  final String createTime;
  final String storeName;
  final String image;
  final String price;

  CollectedProductItem({
    required this.id,
    required this.productId,
    required this.createTime,
    required this.storeName,
    required this.image,
    required this.price,
  });

  factory CollectedProductItem.fromJson(Map<String, dynamic> json) {
    return CollectedProductItem(
      id: json['id'] ?? 0,
      productId: json['productId'] ?? 0,
      createTime: json['createTime'] ?? '',
      storeName: json['storeName'] ?? '',
      image: json['image'] ?? '',
      price: json['price'] ?? '0.00',
    );
  }
}
