import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';

enum HomePlatform {
  all,
  highRebate,
  tiktok,
  // shopee
}

List<HomePlatform> homePlatforms = [
  HomePlatform.all,
  HomePlatform.highRebate,
  HomePlatform.tiktok,
  // HomePlatform.shopee
];

extension HomePlatformDisplayName on HomePlatform {
  String displayName(BuildContext context) {
    switch (this) {
      case HomePlatform.all:
        return S.of(context).home_platform_hot_sale;
      case HomePlatform.highRebate:
        return S.of(context).home_platform_high_rebate;
      case HomePlatform.tiktok:
        return S.of(context).home_platform_tiktok;
      // case HomePlatform.shopee:
      //   return S.of(context).home_platform_shopee;
    }
  }

  int type() {
    switch (this) {
      case HomePlatform.all:
        return 1;
      case HomePlatform.highRebate:
        return 2;
      case HomePlatform.tiktok:
        return 3;
      // case HomePlatform.shopee:
      //   return 4;
    }
  }
}