class InviteeInfo {
  final int uid;
  final String nickname;
  final String avatar;
  final String time;
  final int? level;
  final int childCount;
  final int orderCount;
  final double? numberCount;


  InviteeInfo({
    required this.uid,
    required this.nickname,
    required this.avatar,
    required this.time,
    this.level,
    required this.childCount,
    required this.orderCount,
    this.numberCount,
  });

  factory InviteeInfo.fromJson(Map<String, dynamic> json) {
    return InviteeInfo(
      uid: json['uid'] as int? ?? 0,
      nickname: json['nickname'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      time: json['time'] as String? ?? '',
      level: json['level'] as int?,
      childCount: json['childCount'] as int? ?? 0,
      orderCount: json['orderCount'] as int? ?? 0,
      numberCount: json['numberCount'] != null
          ? double.tryParse(json['numberCount'].toString()) : null,
    );
  }
}


class InviteeInfoResponse {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<InviteeInfo> list;

  InviteeInfoResponse({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory InviteeInfoResponse.fromJson(Map<String, dynamic> json) {
    return InviteeInfoResponse(
      page: json['page'] as int? ?? 1,
      limit: json['limit'] as int? ?? 20,
      totalPage: json['totalPage'] as int? ?? 1,
      total: json['total'] as int? ?? 0,
      list: (json['list'] as List? ?? [])
          .map((item) => InviteeInfo.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }
}


