// models/order_list_response.dart
import 'order_model.dart';

class OrderListResponse {
  final int page;
  final int limit;
  final int totalPage;
  final int total;
  final List<OrderModel> list;

  OrderListResponse({
    required this.page,
    required this.limit,
    required this.totalPage,
    required this.total,
    required this.list,
  });

  factory OrderListResponse.fromJson(Map<String, dynamic> json) {
    var orderList = (json['list'] as List)
        .map((e) => OrderModel.fromJson(e))
        .toList();
    return OrderListResponse(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 20,
      totalPage: json['totalPage'] ?? 1,
      total: json['total'] ?? 0,
      list: orderList,
    );
  }
}
