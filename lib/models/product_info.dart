import 'category.dart';

class ProductResponse {
  dynamic productAttr;
  dynamic productValue;
  dynamic priceName;
  dynamic activityAllH5;
  ProductInfo? productInfo;
  ProductCategory? storeBrand;
  dynamic userCollect;
  String? detailUrl;

  ProductResponse({
    this.productAttr,
    this.productValue,
    this.priceName,
    this.activityAllH5,
    this.productInfo,
    this.storeBrand,
    this.userCollect,
    this.detailUrl,
  });

  factory ProductResponse.fromJson(Map<String, dynamic> json) {
    return ProductResponse(
      productAttr: json['productAttr'],
      productValue: json['productValue'],
      priceName: json['priceName'],
      activityAllH5: json['activityAllH5'],
      productInfo: json['productInfo'] != null
          ? ProductInfo.fromJson(json['productInfo'])
          : null,
      storeBrand: json['storeBrand'] != null
          ? ProductCategory.fromJson(json['storeBrand'])
          : null,
      userCollect: json['userCollect'],
      detailUrl: json['detailUrl']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productAttr': productAttr,
      'productValue': productValue,
      'priceName': priceName,
      'activityAllH5': activityAllH5,
      'productInfo': productInfo?.toJson(),
      'storeBrand': storeBrand?.toJson(),
      'userCollect': userCollect,
      'detailUrl': detailUrl
    };
  }
}

class ProductInfo {
  int? id;
  int? merId;
  String? outProductId;
  dynamic brand;
  String? image;
  String? sliderImage;
  String? storeName;
  String? storeInfo;
  String? keyword;
  String? barCode;
  String? cateId;
  String? price;
  dynamic salesPrice;
  dynamic minSalesPrice;
  dynamic maxSalesPrice;
  String? vipPrice;
  String? otPrice;
  String? channel;
  String? cashBackRate;
  dynamic cashBackAmount;
  String? postage;
  String? unitName;
  int? sort;
  int? sales;
  int? stock;
  bool? isShow;
  bool? isHot;
  bool? isBenefit;
  bool? isBest;
  bool? isNew;
  int? addTime;
  bool? isPostage;
  bool? isRecycle;
  bool? isDel;
  bool? merUse;
  int? giveIntegral;
  String? cost;
  bool? isSeckill;
  dynamic isBargain;
  bool? isGood;
  bool? isSub;
  int? ficti;
  int? browse;
  String? codePath;
  String? soureLink;
  String? videoLink;
  int? tempId;
  bool? specType;
  String? activity;
  String? flatPattern;
  String? shopName;
  dynamic content;

  ProductInfo({
    this.id,
    this.merId,
    this.outProductId,
    this.brand,
    this.image,
    this.sliderImage,
    this.storeName,
    this.storeInfo,
    this.keyword,
    this.barCode,
    this.cateId,
    this.price,
    this.salesPrice,
    this.minSalesPrice,
    this.maxSalesPrice,
    this.vipPrice,
    this.otPrice,
    this.channel,
    this.cashBackRate,
    this.cashBackAmount,
    this.postage,
    this.unitName,
    this.sort,
    this.sales,
    this.stock,
    this.isShow,
    this.isHot,
    this.isBenefit,
    this.isBest,
    this.isNew,
    this.addTime,
    this.isPostage,
    this.isRecycle,
    this.isDel,
    this.merUse,
    this.giveIntegral,
    this.cost,
    this.isSeckill,
    this.isBargain,
    this.isGood,
    this.isSub,
    this.ficti,
    this.browse,
    this.codePath,
    this.soureLink,
    this.videoLink,
    this.tempId,
    this.specType,
    this.activity,
    this.flatPattern,
    this.shopName,
    this.content,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      id: json['id'] as int?,
      merId: json['merId'] as int?,
      outProductId: json['outProductId'] as String?,
      brand: json['brand'],
      image: json['image'] as String?,
      sliderImage: json['sliderImage'] as String?,
      storeName: json['storeName'] as String?,
      storeInfo: json['storeInfo'] as String?,
      keyword: json['keyword'] as String?,
      barCode: json['barCode'] as String?,
      cateId: json['cateId'] as String?,
      price: json['price'] as String?,
      salesPrice: json['salesPrice'],
      minSalesPrice: json['minSalesPrice'],
      maxSalesPrice: json['maxSalesPrice'],
      vipPrice: json['vipPrice'] as String?,
      otPrice: json['otPrice'] as String?,
      channel: json['channel'] as String?,
      cashBackRate: json['cashBackRate'] as String?,
      cashBackAmount: json['cashBackAmount'],
      postage: json['postage'] as String?,
      unitName: json['unitName'] as String?,
      sort: json['sort'] as int?,
      sales: json['sales'] as int?,
      stock: json['stock'] as int?,
      isShow: json['isShow'] as bool?,
      isHot: json['isHot'] as bool?,
      isBenefit: json['isBenefit'] as bool?,
      isBest: json['isBest'] as bool?,
      isNew: json['isNew'] as bool?,
      addTime: json['addTime'] as int?,
      isPostage: json['isPostage'] as bool?,
      isRecycle: json['isRecycle'] as bool?,
      isDel: json['isDel'] as bool?,
      merUse: json['merUse'] as bool?,
      giveIntegral: json['giveIntegral'] as int?,
      cost: json['cost'] as String?,
      isSeckill: json['isSeckill'] as bool?,
      isBargain: json['isBargain'],
      isGood: json['isGood'] as bool?,
      isSub: json['isSub'] as bool?,
      ficti: json['ficti'] as int?,
      browse: json['browse'] as int?,
      codePath: json['codePath'] as String?,
      soureLink: json['soureLink'] as String?,
      videoLink: json['videoLink'] as String?,
      tempId: json['tempId'] as int?,
      specType: json['specType'] as bool?,
      activity: json['activity'] as String?,
      flatPattern: json['flatPattern'] as String?,
      shopName: json['shopName'] as String?,
      content: json['content'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'merId': merId,
      'outProductId': outProductId,
      'brand': brand,
      'image': image,
      'sliderImage': sliderImage,
      'storeName': storeName,
      'storeInfo': storeInfo,
      'keyword': keyword,
      'barCode': barCode,
      'cateId': cateId,
      'price': price,
      'salesPrice': salesPrice,
      'minSalesPrice': minSalesPrice,
      'maxSalesPrice': maxSalesPrice,
      'vipPrice': vipPrice,
      'otPrice': otPrice,
      'channel': channel,
      'cashBackRate': cashBackRate,
      'cashBackAmount': cashBackAmount,
      'postage': postage,
      'unitName': unitName,
      'sort': sort,
      'sales': sales,
      'stock': stock,
      'isShow': isShow,
      'isHot': isHot,
      'isBenefit': isBenefit,
      'isBest': isBest,
      'isNew': isNew,
      'addTime': addTime,
      'isPostage': isPostage,
      'isRecycle': isRecycle,
      'isDel': isDel,
      'merUse': merUse,
      'giveIntegral': giveIntegral,
      'cost': cost,
      'isSeckill': isSeckill,
      'isBargain': isBargain,
      'isGood': isGood,
      'isSub': isSub,
      'ficti': ficti,
      'browse': browse,
      'codePath': codePath,
      'soureLink': soureLink,
      'videoLink': videoLink,
      'tempId': tempId,
      'specType': specType,
      'activity': activity,
      'flatPattern': flatPattern,
      'shopName': shopName,
      'content': content,
    };
  }
}