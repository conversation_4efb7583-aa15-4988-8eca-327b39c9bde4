// models/recharge_config.dart
class RechargeConfig {
  final String diamondRewardPer10Partner;
  final String idPartner;
  final String secondLevelInviteRewardAgent;
  final String thirdLevelInviteRewardAgent;
  final String goldRewardPer10Agent;
  final String diamondRewardPer10Agent;
  final String agentFee;
  final String minWithdrawAmount;
  final String idAgent;
  final String withdrawFeeRate;
  final String maxWithdrawAmount;
  final String goldRewardPer10Partner;
  final String partnerFee;
  final String thirdLevelInviteRewardPartner;
  final String id;
  final String secondLevelInviteRewardPartner;
  final String directInviteRewardAgent;

  RechargeConfig({
    required this.diamondRewardPer10Partner,
    required this.idPartner,
    required this.secondLevelInviteRewardAgent,
    required this.thirdLevelInviteRewardAgent,
    required this.goldRewardPer10Agent,
    required this.diamondRewardPer10Agent,
    required this.agentFee,
    required this.minWithdrawAmount,
    required this.idAgent,
    required this.withdrawFeeRate,
    required this.maxWithdrawAmount,
    required this.goldRewardPer10Partner,
    required this.partner<PERSON>ee,
    required this.thirdLevelInviteRewardPartner,
    required this.id,
    required this.secondLevelInviteRewardPartner,
    required this.directInviteRewardAgent,
  });

  factory RechargeConfig.fromJson(Map<String, dynamic> json) {
    return RechargeConfig(
      diamondRewardPer10Partner: json['diamond_reward_per_10_partner'] ?? '0',
      idPartner: json['id_partner'] ?? '',
      secondLevelInviteRewardAgent: json['second_level_invite_reward_agent'] ?? '0',
      thirdLevelInviteRewardAgent: json['third_level_invite_reward_agent'] ?? '0',
      goldRewardPer10Agent: json['gold_reward_per_10_agent'] ?? '0',
      diamondRewardPer10Agent: json['diamond_reward_per_10_agent'] ?? '0',
      agentFee: json['agent_fee'] ?? '0',
      minWithdrawAmount: json['min_withdraw_amount'] ?? '0',
      idAgent: json['id_agent'] ?? '',
      withdrawFeeRate: json['withdraw_fee_rate'] ?? '0',
      maxWithdrawAmount: json['max_withdraw_amount'] ?? '0',
      goldRewardPer10Partner: json['gold_reward_per_10_partner'] ?? '0',
      partnerFee: json['partner_fee'] ?? '0',
      thirdLevelInviteRewardPartner: json['third_level_invite_reward_partner'] ?? '0',
      id: json['id'] ?? '',
      secondLevelInviteRewardPartner: json['second_level_invite_reward_partner'] ?? '0',
      directInviteRewardAgent: json['direct_invite_reward_agent'] ?? '0',
    );
  }
}