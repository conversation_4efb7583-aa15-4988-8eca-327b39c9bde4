// models/revenue_info.dart
class RevenueInfo {
  final String estimatedTotalAmount;
  final String pendingAmount;
  final String receivedAmount;
  final String withdrawableAmount;
  final String estimatedTodayAmount;

  RevenueInfo({
    required this.estimatedTotalAmount,
    required this.pendingAmount,
    required this.receivedAmount,
    required this.withdrawableAmount,
    required this.estimatedTodayAmount,
  });

  factory RevenueInfo.fromJson(Map<String, dynamic> json) {
    return RevenueInfo(
      estimatedTotalAmount: json['estimatedTotalAmount'] ?? '0',
      pendingAmount: json['pendingAmount'] ?? '0',
      receivedAmount: json['receivedAmount'] ?? '0',
      withdrawableAmount: json['withdrawableAmount'] ?? '0',
      estimatedTodayAmount: json['estimatedTodayAmount'] ?? '0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'estimatedTotalAmount': estimatedTotalAmount,
      'pendingAmount': pendingAmount,
      'receivedAmount': receivedAmount,
      'withdrawableAmount': withdrawableAmount,
      'estimatedTodayAmount': estimatedTodayAmount,
    };
  }
}
