class ShareLinkProductInfo {
  final String productId;
  final String productName;
  final String productImageUrl;
  final String? channel;
  final String shareLink;
  final String tags;

  ShareLinkProductInfo({
    required this.productId,
    required this.productName,
    required this.productImageUrl,
    this.channel,
    required this.shareLink,
    required this.tags,
  });

  factory ShareLinkProductInfo.fromJson(Map<String, dynamic> json) {
    return ShareLinkProductInfo(
      productId: json['productId'],
      productName: json['productName'],
      productImageUrl: json['productImageUrl'],
      channel: json['channel'],
      shareLink: json['shareLink'],
      tags: json['tags'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'productImageUrl': productImageUrl,
      'channel': channel,
      'shareLink': shareLink,
      'tags': tags,
    };
  }
}
