import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';

enum TransactionDetailFilter {
  all,
  income,
  expenditure
}

List<TransactionDetailFilter> transactionDetailFilters = [
  TransactionDetailFilter.all,
  TransactionDetailFilter.income,
  TransactionDetailFilter.expenditure,
];

extension TransactionDetailFilterDisplayName on TransactionDetailFilter {
  String displayName(BuildContext context) {
    switch (this) {
      case TransactionDetailFilter.all:
        return S.of(context).all;
      case TransactionDetailFilter.income:
        return S.of(context).income;
      case TransactionDetailFilter.expenditure:
        return S.of(context).expenditure;
    }
  }

  String value() {
    switch (this) {
      case TransactionDetailFilter.all:
        return "all";
      case TransactionDetailFilter.income:
        return "income";
      case TransactionDetailFilter.expenditure:
        return "expenditure";
    }
  }
}

extension TransactionDetailFilterImages on TransactionDetailFilter {
  String image(BuildContext context) {
    switch (this) {
      case TransactionDetailFilter.all:
        return Assets.imagesIcTransactionDetailAll;
      case TransactionDetailFilter.income:
        return Assets.imagesIcTransactionDetailIncome;
      case TransactionDetailFilter.expenditure:
        return Assets.imagesIcTransactionDetailExpenditure;
    }
  }

  String selectedImage(BuildContext context) {
    switch (this) {
      case TransactionDetailFilter.all:
        return Assets.imagesIcTransactionDetailAllSelected;
      case TransactionDetailFilter.income:
        return Assets.imagesIcTransactionDetailIncomeSelected;
      case TransactionDetailFilter.expenditure:
        return Assets.imagesIcTransactionDetailExpenditureSelected;
    }
  }
}