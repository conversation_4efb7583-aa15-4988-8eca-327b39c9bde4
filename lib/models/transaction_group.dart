// models/transaction_group.dart
import 'transaction_item.dart';

class TransactionGroup {
  final String date;
  final List<TransactionItem> list;

  TransactionGroup({
    required this.date,
    required this.list,
  });

  factory TransactionGroup.fromJson(Map<String, dynamic> json) {
    var items = (json['list'] as List)
        .map((item) => TransactionItem.fromJson(item))
        .toList();
    return TransactionGroup(
      date: json['date'] ?? '',
      list: items,
    );
  }
}
