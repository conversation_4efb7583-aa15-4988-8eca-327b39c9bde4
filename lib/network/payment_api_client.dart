import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../models/payment_models.dart';
import '../utils/tiktok_login.dart';

PaymentApiClient paymentApiClient = PaymentApiClient();

class PaymentApiClient {
  static const String _baseUrl = "https://pay.gencobse.com";
  late final Dio _dio;

  PaymentApiClient() {
    _dio = Dio()
      ..options.baseUrl = _baseUrl
      ..options.contentType = Headers.jsonContentType
      ..options.connectTimeout = const Duration(seconds: 10)
      ..options.receiveTimeout = const Duration(seconds: 10)
      ..options.responseType = ResponseType.json;

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: false,
        ),
      );
    }
  }

  /// 创建支付订单
  Future<CreatePayResponse> createPay({
    required String payType,
    required String type,
    required String reusability,
    required String referenceId,
    required String givenNames,
    required String surname,
    required String channelCode,
    required String amount,
    Map<String, dynamic>? metadata,
  }) async {
    String callbackUrl = getCallbackUrl();
    try {
      final request = CreatePayRequest(
        payType: payType,
        type: type,
        reusability: reusability,
        customer: Customer(
          referenceId: referenceId,
          type: "INDIVIDUAL",
          individualDetail: IndividualDetail(
            givenNames: givenNames,
            surname: surname,
          ),
        ),
        ewallet: Ewallet(
          channelCode: channelCode,
          channelProperties: ChannelProperties(
            successReturnUrl:
            callbackUrl,
            failureReturnUrl:
            callbackUrl,
            cancelReturnUrl:
            callbackUrl,
          ),
        ),
        metadata: metadata,
        amount: amount,
        currency: "IDR",
        orderDescription: "App Recharge",
      );

      final response = await _dio.post(
        '/api/v1/create_pay',
        data: request.toJson(),
        options: Options(contentType: Headers.jsonContentType),
      );

      if (response.statusCode == 200) {
        return CreatePayResponse.fromJson(response.data);
      } else {
        throw _handleError(response);
      }
    } on DioException catch (e) {
      Logger().e("Dio error: ${e.message}", error: e.error, stackTrace: e.stackTrace);
      rethrow;
    } catch (e) {
      Logger().e("Unexpected error: $e");
      rethrow;
    }
  }

  /// 错误处理
  Never _handleError(Response response) {
    final statusCode = response.statusCode ?? 500;
    final errorData = response.data?['error'] ?? response.data;
    final errorMessage = errorData?['message'] ?? 'Payment request failed';

    throw PaymentException(
      statusCode: statusCode,
      message: errorMessage,
      data: errorData,
    );
  }
}

class PaymentException implements Exception {
  final int statusCode;
  final String message;
  final dynamic data;

  PaymentException({
    required this.statusCode,
    required this.message,
    this.data,
  });

  @override
  String toString() =>
      'PaymentException: $statusCode - $message${data != null ? '\nData: $data' : ''}';
}