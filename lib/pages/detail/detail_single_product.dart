import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/string.dart';
import 'package:milestone/widget/image_widget.dart';

class DetailSingleProduct extends StatefulWidget {
  final Product product;
  final int padding;
  const DetailSingleProduct({
    super.key,
    required this.product,
    this.padding = 8,
  });

  @override
  State<DetailSingleProduct> createState() => _DetailSingleProductState();
}

class _DetailSingleProductState extends State<DetailSingleProduct> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(6.0)),
      ),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          renderContent(),
          // Price
          Container(
            padding: EdgeInsets.only(left: 6, bottom: 6, top: 6),
            child: Text(
              "Rp${widget.product.salesPrice.formatIDR()}",
              style: const TextStyle(
                color: highlightTextColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          buildRebateRateButton(context),
          SizedBox(height: 16)
        ],
      ),
    );
  }

  Widget buildRebateRateButton(BuildContext context) {
    return Stack(
      children: [
        Image.asset(R.assetsImagesBgDetailBrandProductCashbackButton, fit: BoxFit.cover,),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).home_cashback_button_title,
                style: TextStyle(color: cashbackTextColor, fontSize: 9),
              ),
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [Colors.white, Colors.white],
                ).createShader(bounds),
                child: Text(
                  'Rp${widget.product.cashBackAmount.formatIDR()}',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: cashbackTextColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget renderContent() {
    return Stack(
      children: [
        ImageWidget(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomLeft: Radius.zero,
            bottomRight: Radius.zero,
          ),
          width: 120,
          height: 120,
          loadingHeight: 120,
          loadingWidth: 120,
          url: widget.product.mainImageUrl,
          fit: BoxFit.cover,
          key: Key("${widget.product.id}"),
        ),
      ],
    );
  }
}
