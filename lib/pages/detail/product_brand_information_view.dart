import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/pages/detail/product_detail_screen.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../models/brand_product_filter.dart';
import '../../models/category.dart';
import '../../models/product_list.dart';
import '../../network/network_api_client.dart';
import '../../r.dart';
import '../../themes/colors.dart';
import '../../utils/navigation_route.dart';
import '../../widget/image_widget.dart';
import '../../widget/loading_error_view.dart';
import '../brand/brand_product_list_screen.dart';
import 'detail_single_product.dart';

class ProductBrandInformation extends StatefulWidget {
  final ProductCategory category;

  const ProductBrandInformation({super.key, required this.category});

  @override
  State<ProductBrandInformation> createState() => ProductBrandInformationState();
}

class ProductBrandInformationState extends State<ProductBrandInformation> {
  ProductListResponse? productListResponse;
  Future? data;

  @override
  void initState() {
    super.initState();
    data = startLoading();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 6, top: 6),
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        color: Colors.white,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              customRouter(context, BrandProductListScreen(category: widget.category));
            },
            child: Row(
              children: [
                ClipOval(
                  child: ImageWidget(
                    width: 22,
                    height: 22,
                    url: widget.category.logoUrl,
                    defaultImagePath: Assets.imagesIcAvatarDefault,
                    loadingWidth: 22,
                    loadingHeight: 22,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  widget.category.name ?? "",
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                Text(
                  "${S.of(context).detail_brand_product_amount_pre} ${productListResponse?.list.length ?? 0} ${S.of(context).detail_brand_product_amount}",
                  style: const TextStyle(
                    color: unSelectedTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Image.asset(R.assetsImagesIcDetailArrowRight),
              ],
            ),
          ),
          SizedBox(height: 8),
          FutureBuilder(builder: buildHomeContent, future: data)
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      height: 120,
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Center(
        child: CupertinoActivityIndicator(),
      ),
    );
  }

  Future<void> startLoading() async {
    try {
      BrandProductFilter brandProductFilter = BrandProductFilter.all;
      bool isAsc = false;
      productListResponse = await networkApiClient.getBrandCategoryProducts(
          widget.category.code,
          brandProductFilter,
          isAsc
      );
      // 确保在加载完成后刷新UI
      if (mounted) setState(() {});
    } catch (e) {
      Logger().d("加载品牌产品失败: $e");
      rethrow;
    }
  }

  Widget buildHomeContent(BuildContext context, AsyncSnapshot snapshot) {
    switch (snapshot.connectionState) {
      case ConnectionState.none:
      case ConnectionState.waiting:
        return _buildLoadingIndicator();
      case ConnectionState.active:
      case ConnectionState.done:
        if (snapshot.hasError) {
          return LoadingErrorView(
            error: "${snapshot.error}",
            onRetry: () {
              startLoading();
            },
          );
        } else {
          return buildContent(context);
        }
    }
  }

  Widget buildContent(BuildContext context) {
    // 获取产品列表，最多显示10个
    final products = productListResponse?.list.take(10).toList() ?? [];

    if (products.isEmpty) {
      return _buildEmptyView();
    }

    return _buildProductList(products);
  }

  Widget _buildEmptyView() {
    return Container(
      height: 80,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Text(
        S.of(context).loading_more_empty,
        style: TextStyle(color: unSelectedTextColor),
      ),
    );
  }

  Widget _buildProductList(List<Product> products) {
    return SizedBox(
      height: 210, // 固定高度
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        physics: BouncingScrollPhysics(),
        itemBuilder: (context, index) {
          final product = products[index];
          return Container(
            width: 120, // 每个产品的宽度
            margin: EdgeInsets.only(
              left: index == 0 ? 0 : 8,
              right: index == products.length - 1 ? 0 : 8,
            ),
            child: GestureDetector(
              onTap: () {
                customRouter(context, ProductDetailScreen(productId: product.id));
              },
              child: DetailSingleProduct(
                product: product,
                padding: 0,
              ),
            )
          );
        },
      ),
    );
  }
}