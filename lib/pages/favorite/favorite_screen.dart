import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/order_item.dart';
import 'package:milestone/models/order_status.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/favorite/favorite_loading_more.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/empty_view.dart';
import 'package:milestone/widget/loading_more_indicator.dart';
import 'package:milestone/network/errors.dart';

import '../../models/collected_product_item.dart';
import '../detail/product_detail_screen.dart';

class FavoriteScreen extends StatefulWidget {
  const FavoriteScreen({super.key});

  @override
  State<StatefulWidget> createState() => _FavoriteScreenState();
}

class _FavoriteScreenState extends State<FavoriteScreen> {
  late FavoriteLoadingMoreAdapter adapter;
  bool isEditorMode = false;
  Map<int, CollectedProductItem> selectedOrders = {};

  @override
  void initState() {
    adapter = FavoriteLoadingMoreAdapter();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      appBar: buildAppBar(context),
      title: '',
      noSafe: true,
      child: Stack(
        children: [
          SafeArea(child: buildListView(context)),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: buildButtonOperationView(context),
          ),
        ],
      ),
    );
  }

  Widget buildListView(BuildContext context) {
    return LoadingMoreList<CollectedProductItem>(
      ListConfig<CollectedProductItem>(
        sourceList: adapter,
        itemBuilder: (context, item, index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () {
                if (isEditorMode) {
                  setState(() {
                    if (selectedOrders.containsKey(item.id)) {
                      selectedOrders.remove(item.id);
                    } else {
                      selectedOrders[item.id] = item;
                    }
                  });
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          ProductDetailScreen(productId: item.productId),
                    ),
                  );
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      buildSelectView(context, item),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          item.image,
                          width: 90,
                          height: 90,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        WidgetSpan(
                                          child: Image.asset(
                                            R.assetsImagesIcHomeTiktok14,
                                          ),
                                          alignment:
                                              PlaceholderAlignment.middle,
                                        ),
                                        TextSpan(text: ' '),
                                        TextSpan(
                                          text: item.storeName,
                                          style: const TextStyle(
                                            color: primaryTextColor,
                                            fontSize: 13,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              children: [
                                Text(
                                  S.of(context).feedback_cash,
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: secondaryTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(width: 2),
                                Text(
                                  'Rp${item.price}',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: highlightTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Rp${item.price}',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: secondaryTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  '${S.of(context).rebase_rate} 7.21%',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: highlightTextColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
        indicatorBuilder: (context, status) {
          switch (status) {
            case IndicatorStatus.none:
            case IndicatorStatus.loadingMoreBusying:
            case IndicatorStatus.fullScreenBusying:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.noMoreLoad:
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text(
                    S.of(context).loading_more_no_more,
                    style: const TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            case IndicatorStatus.empty:
              return const EmptyView();
            default:
              return const SizedBox();
          }
        },
      ),
    );
  }

  Widget buildSelectView(BuildContext context, CollectedProductItem item) {
    if (isEditorMode) {
      if (selectedOrders.containsKey(item.id)) {
        return InkWell(
          onTap: () {
            setState(() {
              selectedOrders.remove(item.id);
            });
          },
          child: Container(
            margin: EdgeInsets.only(right: 16, left: 12),
            child: Image.asset(R.assetsImagesIcFavoriteButton),
          ),
        );
      } else {
        return InkWell(
          onTap: () {
            setState(() {
              selectedOrders[item.id] = item;
            });
          },
          child: Container(
            margin: EdgeInsets.only(right: 16, left: 12),
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              color: Colors.transparent,
              border: Border.all(width: 1, color: Color(0xFFC5BCBB)),
              borderRadius: BorderRadius.all(Radius.circular(9)),
            ),
          ),
        );
      }
    } else {
      return Container();
    }
  }

  Widget buildButtonOperationView(BuildContext context) {
    if (isEditorMode) {
      return Container(
        padding: EdgeInsets.only(
          top: 8,
          left: 0,
          right: 0,
          bottom: 8 + MediaQuery.of(context).padding.bottom,
        ),
        height: 80 + MediaQuery.of(context).padding.bottom,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.4),
              offset: Offset(0, -4),
              blurRadius: 9.2,
            ),
          ],
        ),
        child: Row(
          children: [
            SizedBox(width: 12),
            buildSelectAllView(context),
            Spacer(),
            TextButton(
              onPressed: () {
                if (selectedOrders.isEmpty) {
                  return;
                }
                selectedOrders.forEach((id, order) async {
                  try {
                    await networkApiClient.removeProductFromFavorites(
                      order.productId,
                    );
                    adapter.remove(order);
                    await adapter.loadMore();
                    setState(() {});
                  } catch (e) {
                    if (context.mounted) {
                      ApiErrorHandler.handleError(e, context);
                    }
                  }
                });
                setState(() {
                  isEditorMode = false;
                });
              },
              child: Container(
                width: 200,
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: selectedOrders.isEmpty
                        ? [
                            Color(0xFFC80D1F).withValues(alpha: 0.5),
                            Color(0xFFFB4143).withValues(alpha: 0.5),
                          ]
                        : [Color(0xFFC80D1F), Color(0xFFFB4143)],
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(30)),
                ),
                child: Center(
                  child: Text(
                    S.of(context).delete,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container();
    }
  }

  Widget buildSelectAllView(BuildContext context) {
    if (isEditorMode) {
      if (selectedOrders.length == adapter.length) {
        return InkWell(
          onTap: () {
            setState(() {
              selectedOrders.clear();
            });
          },
          child: Row(
            children: [
              Image.asset(R.assetsImagesIcFavoriteButton),
              SizedBox(width: 6),
              Text(
                S.of(context).cancel_select_all,
                style: TextStyle(
                  color: primaryTextColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        );
      } else {
        return InkWell(
          onTap: () {
            adapter.toList(growable: false).forEach((item) {
              selectedOrders[item.id] = item;
            });
            setState(() {});
          },
          child: Row(
            children: [
              Container(
                margin: EdgeInsets.only(right: 6, left: 0),
                width: 18,
                height: 18,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(width: 1, color: Color(0xFFC5BCBB)),
                  borderRadius: BorderRadius.all(Radius.circular(9)),
                ),
              ),
              Text(
                S.of(context).select_all,
                style: TextStyle(
                  color: primaryTextColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        );
      }
    } else {
      return Container();
    }
  }

  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      elevation: 0,
      backgroundColor: Colors.transparent,
      centerTitle: true,
      leading: IconButton(
        onPressed: () {
          Navigator.pop(context);
        },
        icon: Image.asset(Assets.imagesIcArrowBack),
      ),
      title: Text(
        S.of(context).my_collection,
        style: TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: primaryTextColor,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            setState(() {
              if (adapter.isEmpty) {
                isEditorMode = false;
                return;
              }
              isEditorMode = !isEditorMode;
            });
          },
          child: Text(
            isEditorMode ? S.of(context).cancel : S.of(context).edit,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
        ),
      ],
    );
  }
}
