import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/network/network_api_client.dart';

import '../../models/invitee_info.dart';
import '../../models/user_level.dart';


class InviteeListSource extends LoadingMoreBase<InviteeInfo> {
  int _page = 1;
  final int _pageSize = 10;
  bool _hasMore = true;
  AgentFilters memberLevelFilter;
  String? sortKey;
  String? isAsc;

  InviteeListSource({required this.memberLevelFilter, this.sortKey, this.isAsc});


  @override
  bool get hasMore => _hasMore;

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    if (_page > 2) {
      _hasMore = false;
      return true;
    }
    InviteeInfoResponse response = await networkApiClient.getTeamMembers(sortKey: sortKey, isAsc: isAsc);
    _hasMore = response.list.length >= response.limit;
    for (var inviteeInfo in response.list) {
      switch(memberLevelFilter) {
        case AgentFilters.all:
          add(inviteeInfo);
        case AgentFilters.agent:
          if(inviteeInfo.level != null && inviteeInfo.level! > 0) {
            add(inviteeInfo);
          }
        case AgentFilters.normal:
          if(inviteeInfo.level != null && inviteeInfo.level! == 0) {
            add(inviteeInfo);
          }else if(inviteeInfo.level == null) {
            add(inviteeInfo);
          }
      }
    }
    _page++;
    return true;
  }
}
