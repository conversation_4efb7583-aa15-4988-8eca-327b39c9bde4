import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/pages/me/faq_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/question_button.dart';
import 'package:milestone/widget/text_with_markup.dart';

class UsageGuidelinesScreen extends StatelessWidget {
  const UsageGuidelinesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgUsageTop,
          width: MediaQuery.of(context).size.width,
          fit: BoxFit.cover,
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            centerTitle: true,
            title: Text(
              S.of(context).usage_guideline_title,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.dark,
            backgroundColor: Colors.transparent,
            leading: IconButton(
              color: Theme.of(context).colorScheme.onSecondary,
              icon: Image.asset(R.assetsImagesIcDetailBackArrow),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            actions: [
              QuestionButton(
                onPressed: () {
                  Navigator.of(
                    context,
                  ).push(MaterialPageRoute(builder: (context) => FAQScreen()));
                },
              ),
            ],
          ),
          body: SingleChildScrollView(child: buildContent(context)),
        ),
      ],
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 20, horizontal: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).usage_guideline_title,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                ),
              ),
              SizedBox(height: 16),
              Text(
                S.of(context).usage_guideline_description,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 25),

        Stack(
          children: [
            Center(
              child: Image.asset(
                R.assetsImagesBgUsageGuidelineStep1,
                width: MediaQuery.of(context).size.width - 8 * 2,
                fit: BoxFit.cover,
              ),
            ),

            Container(
              padding: EdgeInsets.only(top: 55, left: 25, right: 12),
              child: TextWithMarkup(
                text: S.of(context).usage_guideline_step1,
                styles: {
                  "red": TextStyle(
                    color: highlightTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                },
                defaultStyle: TextStyle(
                  color: primaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),

        Container(
          margin: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          width: MediaQuery.of(context).size.width - 8 * 2,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
            color: Colors.white,
          ),
          child: Image.asset(R.assetsImagesImgUsageGuidelineStep1),
        ),

        SizedBox(height: 25),

        Stack(
          children: [
            Center(
              child: Image.asset(
                R.assetsImagesBgUsageGuidelineStep2,
                width: MediaQuery.of(context).size.width - 8 * 2,
                fit: BoxFit.cover,
              ),
            ),

            Container(
              padding: EdgeInsets.only(top: 55, left: 25, right: 12),
              child: TextWithMarkup(
                text: S.of(context).usage_guideline_step2,
                styles: {
                  "red": TextStyle(
                    color: highlightTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                },
                defaultStyle: TextStyle(
                  color: primaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),

        Container(
          margin: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          width: MediaQuery.of(context).size.width - 8 * 2,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
            color: Colors.white,
          ),
          child: Image.asset(R.assetsImagesImgUsageGuidelineStep2),
        ),

        SizedBox(height: 25),

        Stack(
          children: [
            Center(
              child: Image.asset(
                R.assetsImagesBgUsageGuidelineStep3,
                width: MediaQuery.of(context).size.width - 8 * 2,
                fit: BoxFit.cover,
              ),
            ),

            Container(
              padding: EdgeInsets.only(top: 55, left: 25, right: 12),
              child: TextWithMarkup(
                text: S.of(context).usage_guideline_step3,
                styles: {
                  "red": TextStyle(
                    color: highlightTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                },
                defaultStyle: TextStyle(
                  color: primaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),

        Container(
          margin: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          width: MediaQuery.of(context).size.width - 8 * 2,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
            color: Colors.white,
          ),
          child: Image.asset(R.assetsImagesImgUsageGuidelineStep3),
        ),

        SizedBox(height: 50),
      ],
    );
  }
}
