import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/utils/string.dart';

import '../../themes/colors.dart';

class RebateRateButton extends StatelessWidget {
  final String amount;
  const RebateRateButton({super.key, required this.amount});

  @override
  Widget build(BuildContext context) {
    return buildRebateRateButton(context);
  }

  Widget buildRebateRateButton(BuildContext context) {
    return ClipRRect(
      child: Stack(
        children: [
          Image.asset(R.assetsImagesBgHomeCashbackButton),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).home_cashback_button_title,
                  style: TextStyle(color: cashbackTextColor, fontSize: 10),
                ),
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: [Colors.white, Colors.white],
                  ).createShader(bounds),
                  child: Text(
                    'Rp${amount.formatIDR()}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: cashbackTextColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
