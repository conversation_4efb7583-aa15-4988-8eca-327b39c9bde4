import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';
import 'package:milestone/models/home_platform.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/network/network_api_client.dart';

class HomeSinglePageNotifier extends ChangeNotifier {
  HomePlatform homePlatform = HomePlatform.all;
  List<Product> products = [];

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  bool _hasError = false;
  bool get hasError => _hasError;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  Future<bool> loadInitialData() async {
    if (_isLoading) return false;

    try {
      _isLoading = true;
      _hasError = false;
      notifyListeners();

      ProductListResponse productListResponse = await _loadPageData();
      _page = productListResponse.page;
      products.addAll(productListResponse.list);
      Logger().d("productListResponse:${productListResponse.totalPage}");
      _hasMore = productListResponse.total >= productListResponse.limit;
      _page ++; // Increment page for next load

      return true;
    } catch (e) {
      Logger().d(e.toString());
      _hasError = true;
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreData() async {
    if (!_hasMore || _isLoadingMore || _isLoading) return;

    try {
      _isLoadingMore = true;
      notifyListeners();
      ProductListResponse productListResponse = await _loadPageData();
      products.addAll(productListResponse.list);
      _page ++;
      _hasMore = productListResponse.total >= productListResponse.limit;
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  Future<void> refreshData() async {
    loadInitialData();
  }

  Future<ProductListResponse> _loadPageData() async {
    return await networkApiClient.getHotProducts(homePlatform, page: _page, limit: _pageSize);
  }
}
