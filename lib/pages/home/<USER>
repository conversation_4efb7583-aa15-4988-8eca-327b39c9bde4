import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/product_list.dart';
import 'package:milestone/pages/home/<USER>';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/string.dart';
import 'package:milestone/widget/image_widget.dart';

class HomeSingleProduct extends StatefulWidget {
  final int columnCount;
  final Product product;
  final int padding;
  const HomeSingleProduct({
    super.key,
    required this.product,
    required this.columnCount,
    this.padding = 8,
  });

  @override
  State<HomeSingleProduct> createState() => _HomeSingleProductState();
}

class _HomeSingleProductState extends State<HomeSingleProduct> {
  @override
  Widget build(BuildContext context) {
    double width =
        (MediaQuery.of(context).size.width - widget.padding * 2) /
        widget.columnCount;
    double cashbackRate = double.parse(widget.product.cashBackRate) * 100;

    return Card(
      elevation: 0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12.0)),
      ),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          renderContent(),
          // channel icon and title
          Container(
            width: width,
            padding: EdgeInsets.only(left: 6, top: 6, bottom: 3),
            child: Row(
              children: [
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        WidgetSpan(
                          child: Image.asset(R.assetsImagesIcHomeTiktok14),
                          alignment: PlaceholderAlignment.middle,
                        ),
                        TextSpan(text: ' '),
                        TextSpan(
                          text: widget.product.title,
                          style: const TextStyle(
                            color: primaryTextColor,
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),

          // Price
          Container(
            padding: EdgeInsets.only(left: 6, bottom: 6),
            child: Text(
              "Rp${widget.product.salesPrice.formatIDR()}",
              style: const TextStyle(
                color: highlightTextColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // commissionRate
          Container(
            padding: EdgeInsets.only(left: 6, bottom: 6),
            child: Text(
              "${S.of(context).home_rebate_rate_title} ${cashbackRate.toStringAsFixed(2)}%",
              style: const TextStyle(
                color: secondaryTextColor,
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [RebateRateButton(amount: widget.product.cashBackAmount)],
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget renderContent() {
    double width =
        (MediaQuery.of(context).size.width - widget.padding * 2) /
        widget.columnCount;
    return ImageWidget(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
        bottomLeft: Radius.zero,
        bottomRight: Radius.zero,
      ),
      width: width,
      height: width,
      loadingWidth: width,
      loadingHeight: width,
      url: widget.product.mainImageUrl,
      fit: BoxFit.cover,
      key: Key("${widget.product.id}"),
    );
  }
}
