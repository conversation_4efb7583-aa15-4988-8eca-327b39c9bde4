import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/transaction_detail_filter.dart';
import 'package:milestone/models/transaction_detail_model.dart';
import 'package:milestone/pages/income/transaction_details_loading_more.dart';
import 'package:milestone/pages/income/transaction_details_result_screen.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/widget/empty_view.dart';
import 'package:milestone/widget/loading_more_indicator.dart';

import '../../models/transaction_item.dart';

class TransactionDetailListView extends StatefulWidget {
  final TransactionDetailFilter filter;

  const TransactionDetailListView({super.key, required this.filter});

  @override
  State<TransactionDetailListView> createState() =>
      _TransactionDetailListViewState();
}

class _TransactionDetailListViewState extends State<TransactionDetailListView> {
  late TransactionDetailsLoadingMoreAdapter adapter;

  @override
  void initState() {
    super.initState();
    adapter = TransactionDetailsLoadingMoreAdapter();
    adapter.filter = widget.filter;
  }

  @override
  Widget build(BuildContext context) {
    return LoadingMoreList<TransactionItem>(
      ListConfig<TransactionItem>(
        itemBuilder: (context, item, index) {
          final isIncome = item.type == '1';
          return InkWell(
            onTap: () {
              customRouter(context,  TransactionDetailsResultScreen(transactionDetailModel: item));
            },
            child:  Container(
              width: MediaQuery.of(context).size.width - 12 * 2,
              padding: EdgeInsets.all(21),
              margin: EdgeInsets.only(top: 12, left: 12, right: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                // boxShadow: [
                //   BoxShadow(
                //     color: Colors.black.withValues(alpha: 0.1),
                //     blurRadius: 12,
                //     offset: Offset(0, 2),
                //   ),
                // ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      isIncome
                          ? Image.asset(
                        Assets.imagesIcTransactionDetailIncome,
                        color: incomeColor,
                      )
                          : Image.asset(
                        Assets.imagesIcTransactionDetailExpenditure,
                        color: expenseColor,
                      ),
                      SizedBox(width: 12),
                      Text(
                        isIncome
                            ? S.of(context).rebase_income
                            : S.of(context).rebase_expenditure,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: primaryTextColor,
                        ),
                      ),
                      Spacer(),
                      Text(
                        '${isIncome ? '+' : '-'}Rp${item.number.toString()}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: isIncome ? incomeColor : expenseColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  Text(
                    "${item.updateTime}",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: secondaryTextColor,
                    ),
                  ),
                ],
              ),
            )
          );
        },
        sourceList: adapter,
        indicatorBuilder: (context, status) {
          switch (status) {
            case IndicatorStatus.none:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.loadingMoreBusying:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.fullScreenBusying:
              return buildLoadingMoreContent(context);
            case IndicatorStatus.noMoreLoad:
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(S.of(context).loading_more_no_more,
                      style: const TextStyle(
                        color: Color(0xFFCCCCCC),
                        fontSize: 14,
                      ))
                ],
              );
            case IndicatorStatus.empty:
              return EmptyView();
            default:
              return const SizedBox();
          }
        },
      ),
    );
  }
}
