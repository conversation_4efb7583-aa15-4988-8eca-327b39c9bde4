import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/transaction_detail_model.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/dashed_divider.dart';

import '../../models/transaction_item.dart';

class TransactionDetailsResultScreen extends StatelessWidget {
  final TransactionItem transactionDetailModel;
  const TransactionDetailsResultScreen({super.key, required this.transactionDetailModel});

  @override
  Widget build(BuildContext context) {
    final isIncome = transactionDetailModel.type == '1';
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Image.asset(Assets.imagesIcArrowBack),
                ),
                title: Text(
                  S.of(context).income_withdrawal_button,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: Image.asset(
                        Assets.imagesBgTransactionDetail,
                      fit: BoxFit.cover,
                      width: MediaQuery.of(context).size.width - 40,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        isIncome
                            ? Image.asset(Assets.imagesIcIncome47,  width: 47, height: 47,)
                            : Image.asset(Assets.imagesIcExpenditure47, width: 47, height: 47),
                        const SizedBox(height: 10),
                        Text(
                          '${isIncome ? '+' : '-'}Rp${transactionDetailModel.number}',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            color: isIncome ? incomeColor : expenseColor,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                            isIncome ? S.of(context).rebase_income : S.of(context).rebase_expenditure,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: secondaryTextColor
                          ),
                        ),
                        const SizedBox(height: 18),
                        const DashedDivider(height: 1),
                        const SizedBox(height: 18),
                        _buildRow(S.of(context).trade_type,  transactionDetailModel.title),
                        _buildRow(S.of(context).trade_time, transactionDetailModel.updateTime.toString()),
                        _buildRow(S.of(context).trade_serial_number, transactionDetailModel.id ?? ""),
                        _buildRow(S.of(context).trade_channel, transactionDetailModel.type),
                        _buildRow(S.of(context).trade_order_number, transactionDetailModel.id ?? ""),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: secondaryTextColor)),
          Expanded(
            child: Text(value,
              textAlign: TextAlign.right,
              style: const TextStyle(color: primaryTextColor),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}