import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/user_info.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/login/verification_code_screen.dart';
import 'package:milestone/pages/main_screen.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/loading_dialog.dart';

class LoginWithPasswordScreen extends StatefulWidget {
  final String phoneNumber;
  const LoginWithPasswordScreen({super.key, required this.phoneNumber});

  @override
  State<StatefulWidget> createState() => _LoginWithPasswordScreenState();
}

class _LoginWithPasswordScreenState extends State<LoginWithPasswordScreen> {
  bool buttonEnabled = false;
  String password = '';
  bool _obscurePassword = true;

  Future<void> _handleLogin(BuildContext context) async {
    //  实现实际的登录逻辑
    setState(() {
      buttonEnabled = false; // 禁用按钮，防止重复点击
    });
    showLoadingDialog();
    Response response = await networkApiClient.accountLogin(
      account: widget.phoneNumber,
      password: password,
    );
    Logger().d("Login response: $response, ${response.statusMessage}");

    if (!context.mounted) return;
    if (response.statusCode == 200) {
      final Map<String, dynamic> responseBody = response.data is Map
          ? response.data as Map<String, dynamic>
          : json.decode(response.data);
      if (responseBody['code'] == 200) {
        UserManager.saveUserInfo(responseBody["data"]);
        UserInfo userInfo = await networkApiClient.getUserInfo();
        UserManager.saveFullUserInfo(userInfo);
        dismissLoadingDialog();
        makeToast(S.of(context).login_success);
        if(context.mounted) {
          Navigator.of(context, rootNavigator: true).pop();
        }
        return;
      } else {
        dismissLoadingDialog();
        makeToast("${S.of(context).login_failed}: ${responseBody['message']}");
      }
    } else {
      dismissLoadingDialog();
      makeToast("${S.of(context).login_failed}, ${response.statusMessage}");
    }
    setState(() {
      buttonEnabled = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: "",
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).welcome_back,
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
            ),
            SizedBox(height: 12),
            Text(
              S.of(context).please_input_your_password,
              style: TextStyle(
                fontSize: 13,
                color: secondaryTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 25),
            Text(
              S.of(context).login_password,
              style: TextStyle(
                fontSize: 13,
                color: secondaryTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
              decoration: BoxDecoration(
                color: Color(0xFFF9F8F7),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: TextField(
                      keyboardType: TextInputType.text,
                      onChanged: (value) async {
                        setState(() {
                          password = value;
                          buttonEnabled = password.isNotEmpty;
                        });
                      },
                      obscureText: _obscurePassword,
                      decoration: InputDecoration(
                        hintText: S.of(context).input_password_hint,
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: secondaryTextColor,
                        )
                      ),
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: primaryTextColor,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    child: _obscurePassword
                        ? Image.asset(R.assetsImagesIcEyeClose)
                        : Image.asset(R.assetsImagesIcEyeOpen),
                  )
                ],
              ),
            ),
            SizedBox(height: 22),
            InkWell(
              onTap: () async {
                if (!buttonEnabled) {
                  return;
                }
                if (password.isNotEmpty) {
                  _handleLogin(context);
                }
              },
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 0),
                width: MediaQuery.of(context).size.width,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: buttonEnabled
                        ? [Color(0xFFE6AC44), Color(0xFFFACD8A)]
                        : [Color(0x7FE6AC44), Color(0x7FE5B670)],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    S.of(context).login,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 12),
            Center(
              child: TextButton(
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    "login_with_whatsapp"
                  );
                },
                child: Text(
                  S.of(context).login_with_verification_code,
                  style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
