import 'package:flutter/material.dart';
import 'package:milestone/pages/me/step_list_view.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/themes/colors.dart';

import '../../generated/l10n.dart';
import '../../r.dart';

class LevelUpView extends StatefulWidget {
  final MemberSingleLevel memberSingleLevel;
  final int step;
  const LevelUpView({
    super.key,
    required this.memberSingleLevel,
    required this.step
  });

  @override
  State<StatefulWidget> createState() => LevelUpViewState();
}

class LevelUpViewState extends State<LevelUpView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                S.of(context).level_up_schedule,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                "(${widget.step}/${widget.memberSingleLevel.ratio() * 10})",
                style: TextStyle(
                  color: Color(0xFF98938F),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Spacer(),
              Text(
                S.of(context).level_up_description,
                style: TextStyle(
                  color: Color(0xFF98938F),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Image.asset(
                R.assetsImagesIcMeArrowLeft,
                color: Color(0xFF98938F),
              ),
            ],
          ),
          SizedBox(height: 10),
          StepListView(total: 10, step: widget.step, stepText: widget.memberSingleLevel.levelReward(), targetText: widget.memberSingleLevel.nextLevel().fullDisplayName(context), ratio: widget.memberSingleLevel.ratio()),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(R.assetsImagesIcMeDecorationLeft),
              Expanded(
                child: Text(
                  S.of(context).level_up_content_title,
                  style: TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Image.asset(R.assetsImagesIcMeDecorationRight),
            ],
          ),
        ],
      ),
    );
  }


}
