import 'dart:async';

import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/utils/user_utils.dart';


class MobileLoginPage extends StatefulWidget {
  final Function(bool) onLoginSuccess; // 登录成功回调
  final VoidCallback? onLoginSuccessCallback; // 登录成功回调函数

  const MobileLoginPage({
    super.key,
    required this.onLoginSuccess,
    this.onLoginSuccessCallback,
  });

  @override
  State<MobileLoginPage> createState() => _MobileLoginPageState();
}

class _MobileLoginPageState extends State<MobileLoginPage> {
  final _phoneController = TextEditingController();
  final _captchaController = TextEditingController();
  final _apiClient = NetworkApiClient();
  final _logger = Logger();

  int _countdown = 0;
  bool _isLoading = false;
  Timer? _countdownTimer;

  @override
  void dispose() {
    _phoneController.dispose();
    _captchaController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  // 发送验证码
  Future<void> _sendVerificationCode() async {
    final phone = _phoneController.text.trim();
    if (phone.isEmpty) {
      _showSnackBar(S.of(context).login_enter_phone);
      return;
    }

    setState(() => _countdown = 60);
    _startCountdown();

    try {
      // 实际手机号需要拼接区号 +62
      final phoneNumber = '+62$phone';
      await _apiClient.sendVerificationCode(phoneNumber);
      _showSnackBar(S.of(context).login_code_sent);
    } catch (e) {
      _showSnackBar('${S.of(context).login_send_failed}: ${e.toString()}');
    }
  }

  // 启动倒计时
  void _startCountdown() {
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() => _countdown--);
      } else {
        timer.cancel();
      }
    });
  }

  // 手机号登录
  Future<void> _loginWithMobile() async {
    final phone = _phoneController.text.trim();
    final code = _captchaController.text.trim();

    if (phone.isEmpty || code.isEmpty) {
      _showSnackBar(S.of(context).login_enter_phone_code);
      return;
    }

    setState(() => _isLoading = true);

    try {
      // 实际手机号需要拼接区号 +62
      final phoneNumber = '+62$phone';
      final response = await _apiClient.mobileLogin(
        phone: phoneNumber,
        captcha: code,
      );

      // 登录成功处理
      if (response.statusCode == 200) {
        // 更新用户登录状态
        //UserUtils.setLoginStatus(true);

        // 执行成功回调
        widget.onLoginSuccess(true);

        // 调用回调函数（如果有）
        widget.onLoginSuccessCallback?.call();

        // 导航回之前页面（通常是主界面）
        Navigator.of(context).pop(true);
      } else {
        _showSnackBar('${S.of(context).login_failed}: ${response.data}');
      }
    } catch (e) {
      _showSnackBar('${S.of(context).login_failed}: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = S.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.login_mobile_title),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(false),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题区
            Text(
              localizations.login_welcome_back,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              localizations.login_mobile_subtitle,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // 手机号输入区
            Row(
              children: [
                // 固定区号显示
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '+62',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    decoration: InputDecoration(
                      labelText: localizations.login_phone_label,
                      hintText: localizations.login_phone_hint,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 验证码区域
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _captchaController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: localizations.login_code_label,
                      hintText: localizations.login_code_hint,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 120,
                  child: ElevatedButton(
                    onPressed: _countdown > 0 ? null : _sendVerificationCode,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _countdown > 0
                          ? '$_countdown${localizations.login_code_seconds}'
                          : localizations.login_get_code,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // 登录按钮
            ElevatedButton(
              onPressed: _isLoading ? null : _loginWithMobile,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? CircularProgressIndicator(
                color: Theme.of(context).colorScheme.onPrimary,
              )
                  : Text(localizations.login_button),
            ),

            // 其他登录方式
            const Spacer(),
            const Divider(),
            TextButton(
              onPressed: () {
                // 切换到其他登录方式，例如密码登录
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(
                //     builder: (context) => PasswordLoginPage(),
                //   ),
                // );
              },
              child: Text(
                localizations.login_password_alternative,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}