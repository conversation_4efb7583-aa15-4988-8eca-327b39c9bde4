import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/loading_dialog.dart';

import '../../network/errors.dart';
import '../../utils/profile_listener_mixin.dart';

class ModifyNicknameScreen extends StatefulWidget {
  const ModifyNicknameScreen({super.key});

  @override
  State<StatefulWidget> createState() => _ModifyNicknameScreenState();
}

class _ModifyNicknameScreenState extends State<ModifyNicknameScreen> with ProfileListenerMixin {
  final TextEditingController editingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    editingController.text = nickname;
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).modify_nickname,
      actions: [
        TextButton(
          onPressed: () async {
            String nickname = editingController.text;
            if(nickname.trim().isEmpty) {
              makeToast(S.of(context).nickname_hint);
              return;
            }
            if(nickname.length > 10) {
              makeToast(S.of(context).nickname_too_long);
              return;
            }
            try {
              showLoadingDialog();
              String avatar = UserManager.getAvatar();
              await networkApiClient.modifyNickname(nickname, avatar);
              UserManager.updateUserInfo("nikeName", nickname);
              if(context.mounted) {
                dismissLoadingDialog();
                Navigator.of(context).pop();
              }
            } catch (e) {
              if(context.mounted) {
                ApiErrorHandler.handleError(e, context);
                dismissLoadingDialog();
              }
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 4, horizontal: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFE6AC44), Color(0xFFE5B670)],
              ),
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Text(
              S.of(context).finish,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
      child: buildModifyNicknameView(context),
    );
  }

  Widget buildModifyNicknameView(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      padding: EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).nickname,
            style: TextStyle(
              color: primaryTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 8),
          TextField(
            controller: editingController,
            keyboardType: TextInputType.name,
            decoration: InputDecoration(
              hintText: S.of(context).modify_nickname,
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: secondaryTextColor,
              ),
            ),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
