import 'package:flutter/material.dart';

import '../../r.dart';
import '../../themes/colors.dart';

class StepListView extends StatelessWidget {
  final int ratio;
  final int total;
  final int step;
  final String stepText;
  final String targetText;

  const StepListView({
    super.key,
    required this.total,
    required this.step,
    required this.stepText,
    required this.targetText,
    this.ratio = 0
  });

  @override
  Widget build(BuildContext context) {
    return _buildStepViewList();
  }

  Widget _buildStepViewList() {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: total,
        physics: BouncingScrollPhysics(),
        itemBuilder: (context, index) {
          return buildSingleBadgeView(context, index, step);
        },
      ),
    );
  }

  Widget buildSingleBadgeView(BuildContext context, int index, int value) {
    double singleWidth = 80;
    return SizedBox(
      width: singleWidth,
      child: Column(
        children: [
          (index == total - 1)
              ? Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Image.asset(R.assetsImagesBgLevelUpTarget),
                    Text(
                      targetText,
                      style: TextStyle(
                        color: primaryTextColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                )
              : SizedBox(height: 19),
          Stack(
            alignment: Alignment.center,
            children: [
              Row(
                children: [
                  Container(
                    width: singleWidth / 2,
                    height: 5,
                    decoration: BoxDecoration(
                      color: value > index
                          ? Color(0xFFFDB242)
                          : Color(0xFFF5F3F2),
                      borderRadius: BorderRadius.horizontal(
                        left: Radius.circular(index == 0 ? 3 : 0),
                      ),
                    ),
                  ),
                  Container(
                    width: singleWidth / 2,
                    height: 5,
                    decoration: BoxDecoration(
                      color: value > index
                          ? Color(0xFFFDB242)
                          : Color(0xFFF5F3F2),
                      borderRadius: BorderRadius.horizontal(
                        right: Radius.circular((index == total - 1) ? 3 : 0),
                      ),
                    ),
                  ),
                ],
              ),
              Image.asset(
                value > index
                    ? R.assetsImagesIcMeStepCircleCheck
                    : R.assetsImagesIcMeStepCircle,
              ),
              Text(
                ((index + 1) * ratio).toString().padLeft(2, '0'),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ],
          ),
          Text(
            stepText,
            style: TextStyle(
              color: Color(0xFF98938F),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
