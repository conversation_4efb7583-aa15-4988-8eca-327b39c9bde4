import 'dart:math';

import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/pages/member/member_level.dart';
import 'package:milestone/utils/navigation_route.dart';
import 'package:milestone/widget/background_scaffold.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../r.dart';
import '../../themes/colors.dart';
import '../../utils/user_utils.dart';
import '../../widget/bottom_table_info_view.dart';
import '../../widget/image_widget.dart';
import 'animated_list_item.dart';
import 'member_order_payment_screen.dart';

class MemberIntroductionScreen extends StatefulWidget {
  final bool showAll;
  const MemberIntroductionScreen({super.key, this.showAll = false});

  @override
  State<MemberIntroductionScreen> createState() =>
      _MemberIntroductionScreenState();
}

class _MemberIntroductionScreenState extends State<MemberIntroductionScreen>
    with TickerProviderStateMixin {
  MemberLevel currentMemberLevel = MemberLevel.silverAgent;
  late AnimationController _listController;
  late Animation<double> _listAnimation;
  late List<MemberLevel> memberLevels;

  @override
  void initState() {
    super.initState();
    if (widget.showAll) {
      memberLevels = [
        MemberLevel.normal,
        MemberLevel.silverAgent,
        MemberLevel.partners,
      ];
    } else {
      memberLevels = [MemberLevel.silverAgent, MemberLevel.partners];
    }

    // 初始化列表动画控制器
    _listController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _listAnimation = CurvedAnimation(
      parent: _listController,
      curve: Curves.easeInOut,
    );

    // 启动动画
    _listController.forward();
  }

  @override
  void dispose() {
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: Image.asset(Assets.imagesIcArrowBack, color: Colors.white),
        ),
        title: Text(
          S.of(context).member_level_state,
          style: TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      bottomNavigationBar: currentMemberLevel != MemberLevel.normal
          ? becomeMemberButton(context)
          : null,
      backgroundImage: R.assetsImagesBgMemberIntro,
      backgroundColor: backgroundColor,
      title: S.of(context).member_level_state,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildUserContent(context),
          SizedBox(height: 16),
          buildMemberLevelTabBar(context),
          SizedBox(height: 16),
          buildMemberCardView(context),
          Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Text(
              S.of(context).exclusive_benefits,
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: AnimatedSwitcher(
              // 添加整体切换动画
              duration: const Duration(milliseconds: 500),
              transitionBuilder: (child, animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SizeTransition(
                    sizeFactor: animation,
                    axisAlignment: -1.0,
                    child: child,
                  ),
                );
              },
              child: buildMemberBenefitsList(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildMemberBenefitsList(BuildContext context) {
    List<MemberBenefits> benefits = currentMemberLevel.memberBenefitsList(
      context,
    );

    return ListView.builder(
      key: ValueKey(currentMemberLevel), // 为不同等级设置不同key
      scrollDirection: Axis.vertical,
      itemCount: benefits.length,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) {
        final benefit = benefits[index];

        // 为每个列表项添加交错动画
        return AnimatedListItem(
          index: index,
          animation: _listAnimation,
          child: InkWell(
            onTap: () {
              if (benefit.detail != null) {
                showBottomTableInfoView(
                  context,
                  benefit.title,
                  benefit.detail!,
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Color(0xFFFFFEE8), Color(0xFFECC8AF)],
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(child: Image.asset(benefit.imageIcon)),
                  ),
                  const SizedBox(width: 14),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          benefit.title,
                          style: TextStyle(
                            color: primaryTextColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          benefit.description,
                          style: TextStyle(
                            color: secondaryTextColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  benefit.withArrow
                      ? Image.asset(R.assetsImagesIcIncomeArrowRight)
                      : Container(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget becomeMemberButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        customRouter(
          context,
          MemberOrderPaymentScreen(memberLevel: currentMemberLevel),
        );
      },
      child: Container(
        height: 46,
        margin: EdgeInsets.only(left: 16, right: 16, top: 6, bottom: 20 + MediaQuery.of(context).padding.bottom),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFC80D1F), Color(0xFFFB4143)],
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                S.of(context).become_member,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: fourthTextColor,
                ),
              ),
              AnimatedFlipCounter(
                value: currentMemberLevel.fee(),
                thousandSeparator: ',',
                prefix: "Rp",
                textStyle: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: fourthTextColor,
                ),
              ),
              Text(
                "/${S.of(context).year}",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: fourthTextColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildMemberCardView(BuildContext context) {
    double padding = 16;
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: padding, vertical: 14),
          child: Image.asset(
            R.assetsImagesBgMemberVip,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width - padding * 2,
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: padding, vertical: 4),
              padding: EdgeInsets.symmetric(horizontal: 25, vertical: 2),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF6F2F25), Color(0xFF441C04)],
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                "Diskon Waktu terbatas",
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                  color: fourthTextColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 6),
            Text(
              currentMemberLevel.agentFeeActionTitle(context),
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                color: primaryTextColor,
              ),
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              // textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  "Rp",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: primaryTextColor,
                  ),
                ),
                AnimatedFlipCounter(
                  value: currentMemberLevel.fee(),
                  thousandSeparator: ',',
                  textStyle: TextStyle(
                    fontSize: 30,
                    fontWeight: FontWeight.w700,
                    color: primaryTextColor,
                  ),
                ),
                Text(
                  "/${S.of(context).year}",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: primaryTextColor,
                  ),
                ),
              ],
            ),

            Text(
              "Rp${currentMemberLevel.feeOriginal()}/年",
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                color: Color(0xFFA79184),
                decoration: TextDecoration.lineThrough,
                decorationColor: Color(0xFFA79184),
              ),
            ),
          ],
        ),

        Positioned(
          top: 0,
          right: padding,
          child: Transform.rotate(
            // 包裹整个 Stack
            angle: 20 * (pi / 180), // 旋转45度（逆时针）
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(R.assetsImagesBgMemberOff),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "90%",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        height: 12 / 13,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      "OFF",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        height: 12 / 13,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget buildMemberLevelTabBar(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Image.asset(
          currentMemberLevel.tabBackground(showAll: widget.showAll),
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: memberLevels.map((value) {
            return InkWell(
              onTap: () {
                setState(() {
                  currentMemberLevel = value;
                });
              },
              child: SizedBox(
                height: 42,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      R.assetsImagesIcMemberSilverAgent,
                      color: currentMemberLevel == value
                          ? Color(0xFFB76928)
                          : Colors.white,
                    ),
                    SizedBox(width: 4),
                    Text(
                      value.displayName(context),
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: currentMemberLevel == value
                            ? Color(0xFFB76928)
                            : Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),

          // [
          //
          //   InkWell(
          //     onTap: () {
          //       setState(() {
          //         currentMemberLevel = MemberLevel.partners;
          //       });
          //     },
          //     child: SizedBox(
          //       height: 42,
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.center,
          //         crossAxisAlignment: CrossAxisAlignment.center,
          //         children: [
          //           Image.asset(
          //             R.assetsImagesIcMemberPartners,
          //             color: currentMemberLevel == MemberLevel.partners
          //                 ? Color(0xFFB76928)
          //                 : Colors.white,
          //           ),
          //           SizedBox(width: 4),
          //           Text(
          //             MemberLevel.partners.displayName(context),
          //             style: TextStyle(
          //               fontSize: 15,
          //               fontWeight: FontWeight.w600,
          //               color: currentMemberLevel == MemberLevel.partners
          //                   ? Color(0xFFB76928)
          //                   : Colors.white,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //   ),
          // ],
        ),
      ],
    );
  }

  Widget buildUserContent(BuildContext context) {
    double imageWidth = 44;
    return Container(
      padding: EdgeInsets.only(top: 12, left: 12, right: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipOval(
            child: ImageWidget(
              width: imageWidth,
              height: imageWidth,
              url: "avatar",
              defaultImagePath: Assets.imagesIcAvatarDefault,
              loadingWidth: imageWidth,
              loadingHeight: imageWidth,
            ),
          ),
          SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                UserManager.getNickname() ?? "----",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: fourthTextColor,
                ),
              ),
              SizedBox(height: 4),
              Text(
                S.of(context).member_status_description,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: fourthTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
