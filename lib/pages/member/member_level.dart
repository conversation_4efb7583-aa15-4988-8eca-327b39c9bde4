import 'package:flutter/material.dart';
import 'package:milestone/main.dart';
import 'package:milestone/themes/colors.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../r.dart';
import 'member_single_level.dart';

enum MemberLevel { normal, silverAgent, partners }

List<MemberLevel> memberLevels = [
  MemberLevel.normal,
  MemberLevel.silverAgent,
  MemberLevel.partners,
];

extension MemberLevelDisplay on MemberLevel {
  String paymentValue() {
    switch(this) {
      case MemberLevel.normal: return "normal";
      case MemberLevel.silverAgent: return "agent";
      case MemberLevel.partners: return "partner";
    }
  }

  String displayName(BuildContext context) {
    switch (this) {
      case MemberLevel.normal:
        return S.of(context).normal_member_user;
      case MemberLevel.silverAgent:
        return S.of(context).member_introduction_level_silver_agent;
      case MemberLevel.partners:
        return S.of(context).member_level_partner;
    }
  }

  List<MemberSingleLevel> singleLevels() {
    switch (this) {
      case MemberLevel.normal:
        return [];
      case MemberLevel.silverAgent:
        return [MemberSingleLevel.silver, MemberSingleLevel.gold, MemberSingleLevel.diamond, MemberSingleLevel.partner];
      case MemberLevel.partners:
        return [MemberSingleLevel.partnerSilver, MemberSingleLevel.partnerGold, MemberSingleLevel.partnerDiamond];
    }
  }

  Color backgroundColor() {
    switch (this) {
      case MemberLevel.normal:
      case MemberLevel.silverAgent:
        return Colors.white;
      case MemberLevel.partners:
        return Color(0xFF06090C);
    }
  }

  Color textColor() {
    switch(this) {
      case MemberLevel.normal:
      case MemberLevel.silverAgent:
        return primaryTextColor;
      case MemberLevel.partners:
        return Color(0xFFF0D0A3);
    }
  }

  Color descriptionTextColor() {
    switch(this) {
      case MemberLevel.normal:
      case MemberLevel.silverAgent:
        return secondaryTextColor;
      case MemberLevel.partners:
        return Colors.white;
    }
  }

  String backgroundImage() {
    switch (this) {
      case MemberLevel.normal:
      case MemberLevel.silverAgent:
        return Assets.imagesBgMemberLevelTop;
      case MemberLevel.partners:
        return R.assetsImagesBgMemberLevelTopBlack;
    }
  }

  String tabBackground({bool showAll = false}) {
    if(showAll) {
      switch (this) {
        case MemberLevel.normal:
          return R.assetsImagesBgMemberLevelTabNormal;
        case MemberLevel.silverAgent:
          return R.assetsImagesBgMemberLevelTabSliverAgent;
        case MemberLevel.partners:
          return R.assetsImagesBgMemberLevelTabPartner;
      }
    }else {
      switch (this) {
        case MemberLevel.normal:
        case MemberLevel.silverAgent:
          return R.assetsImagesBgMemberTabLeft;
        case MemberLevel.partners:
          return R.assetsImagesBgMemberTabRight;
      }
    }
  }

  String agentFeeActionTitle(BuildContext context) {
    switch (this) {
      case MemberLevel.normal:
        return S.of(context).normal_member;
      case MemberLevel.silverAgent:
        return S.of(context).member_level_silver_agent_fee;
      case MemberLevel.partners:
        return S.of(context).member_level_partner_fee;
    }
  }

  int fee() {
    switch (this) {
      case MemberLevel.normal:
        return 0;
      case MemberLevel.silverAgent:
        return int.tryParse(Global.rechargeConfig?.agentFee ?? '0') ?? 159000;
      case MemberLevel.partners:
        return int.tryParse(Global.rechargeConfig?.partnerFee ?? '0') ??999000;
    }
  }

  int feeOriginal() {
    switch (this) {
      case MemberLevel.normal:
        return 0;
      case MemberLevel.silverAgent:
        return 2000000;
      case MemberLevel.partners:
        return 10000000;
    }
  }

  List<MemberBenefits> memberBenefitsList(BuildContext context) {
    switch (this) {
      case MemberLevel.normal:
        return [
          MemberBenefits(
            S.of(context).high_cashback,
            S.of(context).high_cashback_description,
            R.assetsImagesIcMemberBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_2_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_2_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).no_limit,
            S.of(context).no_limit_description,
            R.assetsImagesIcMemberGroupBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_3_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_3_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).user_service,
            S.of(context).user_service_description,
            R.assetsImagesIcMemberExtraCashback,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_4_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_4_benefit,
                ],
              ],
            ),
          ),
        ];
      case MemberLevel.silverAgent:
        return [
          MemberBenefits(
            S.of(context).member_benefits_silver_agent_1,
            S.of(context).member_benefits_silver_agent_1_value,
            R.assetsImagesIcMemberDuration,
            false
          ),
          MemberBenefits(
            S.of(context).member_benefits_silver_agent_2,
            S.of(context).member_benefits_silver_agent_2_value,
            R.assetsImagesIcMemberBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_2_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_2_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).member_benefits_silver_agent_3,
            S.of(context).member_benefits_silver_agent_3_value,
            R.assetsImagesIcMemberGroupBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_3_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_3_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).member_benefits_silver_agent_4,
            S.of(context).member_benefits_silver_agent_4_value,
            R.assetsImagesIcMemberExtraCashback,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_4_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_4_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).member_benefits_silver_agent_5,
            S.of(context).member_benefits_silver_agent_5_value,
            R.assetsImagesIcMemberCashBackNoLimit,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_5_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_5_benefit,
                ],
              ],
            ),
          ),
        ];
      case MemberLevel.partners:
        return [
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_1,
            S.of(context).member_benefits_partner_agent_1_value,
            R.assetsImagesIcMemberDuration,
            false
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_2,
            S.of(context).member_benefits_partner_agent_2_value,
            R.assetsImagesIcMemberBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_2_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_2_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_3,
            S.of(context).member_benefits_partner_agent_3_value,
            R.assetsImagesIcMemberGroupBonus,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_3_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_3_benefit,
                ],
              ],
            ),
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_4,
            S.of(context).member_benefits_partner_agent_4_value,
            R.assetsImagesIcMemberExtraCashback,
            true,
            detail: TableModel(
              headers: [S.of(context).role, S.of(context).benefit],
              rows: [
                [
                  S.of(context).normal_user,
                  S.of(context).normal_user_4_benefit,
                ],
                [
                  S.of(context).agent,
                  S.of(context).member_benefits_silver_agent_4_benefit,
                ],
              ],
            ),
          ),
        ];
    }
  }
}

class TableModel {
  final List<String> headers; // 表头数据
  final List<List<dynamic>> rows; // 行数据（支持动态类型）

  TableModel({required this.headers, required this.rows});
}

class MemberBenefits {
  final String title;
  final String description;
  final String imageIcon;
  final bool withArrow;
  TableModel? detail;

  MemberBenefits(
    this.title,
    this.description,
    this.imageIcon,
    this.withArrow, {
    this.detail,
  });
}

List<MemberBenefits> createMemberBenefitsList(BuildContext context) {
  return [
    MemberBenefits(
      S.of(context).member_benefits_silver_agent_1,
      S.of(context).member_benefits_silver_agent_1_value,
      R.assetsImagesIcMemberDuration,
      false,
    ),
    MemberBenefits(
      S.of(context).member_benefits_silver_agent_2,
      S.of(context).member_benefits_silver_agent_2_value,
      R.assetsImagesIcMemberBonus,
      true,
    ),
    MemberBenefits(
      S.of(context).member_benefits_silver_agent_3,
      S.of(context).member_benefits_silver_agent_3_value,
      R.assetsImagesIcMemberGroupBonus,
      true,
    ),
    MemberBenefits(
      S.of(context).member_benefits_silver_agent_4,
      S.of(context).member_benefits_silver_agent_4_value,
      R.assetsImagesIcMemberExtraCashback,
      true,
    ),
    MemberBenefits(
      S.of(context).member_benefits_silver_agent_5,
      S.of(context).member_benefits_silver_agent_5_value,
      R.assetsImagesIcMemberCashBackNoLimit,
      true,
    ),
  ];
}