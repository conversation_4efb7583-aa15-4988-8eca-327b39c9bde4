import 'package:flutter/material.dart';

import '../../generated/assets.dart';
import '../../generated/l10n.dart';
import '../../widget/gradient_rounded_progress_indicator.dart';
import 'member_single_level.dart';

class MemberLevelView extends StatelessWidget {
  final MemberSingleLevel memberSingleLevel;
  final int progress = 2;
  const MemberLevelView({super.key, required this.memberSingleLevel});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(memberSingleLevel.background(), width: MediaQuery.of(context).size.width, fit: BoxFit.cover),
        Container(
          margin: EdgeInsets.symmetric(vertical: 25, horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                memberSingleLevel.displayName(),
                style: TextStyle(
                  color: Color(0xFF415060),
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Text(
                S.of(context).valid_for,
                style: TextStyle(
                  color: Color(0xFF415060),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(
                S.of(context).upgrade_date,
                style: TextStyle(
                  color: Color(0xFF415060),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(height: 25),
              buildProgressView(context)
            ],
          ),
        )
      ],
    );
  }

  Widget buildProgressView(BuildContext context) {
    if(progress > 0) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.of(context).to_gold_progress,
                style: TextStyle(
                  color: Color(0xFF415060),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              Text(
                "$progress/10",
                style: TextStyle(
                  color: Color(0xFF415060),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          SizedBox(height: 6),
          GradientRoundedProgressIndicator(
            value: progress / 10,
            gradient: LinearGradient(colors: memberSingleLevel.displayColors(), begin: Alignment.topLeft, end: Alignment.bottomRight),
          ),
          SizedBox(height: 6),
          Text(
            S.of(context).invite_to_upgrade,
            style: TextStyle(
              color: Color(0xFF415060),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          )
        ],
      );
    } else {
      return Text(
        S.of(context).invite_to_upgrade_empty,
        style: TextStyle(
          color: Color(0xFF415060),
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      );
    }
  }
}
