import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:milestone/generated/assets.dart';

import '../../generated/l10n.dart';
import '../../r.dart';
import 'member_level.dart';

enum MemberSingleLevel {
  normalUser, silver, gold, diamond, partner, partnerSil<PERSON>, partner<PERSON><PERSON>, partnerDiamond
}

extension MemberSingleLevelDisplay on MemberSingleLevel {
  String displayName() {
    switch(this) {
      case MemberSingleLevel.normalUser:
        return "Pengguna biasa";
      case MemberSingleLevel.silver:
        return "Agen Perak";
      case MemberSingleLevel.gold:
        return "Agen Emas";
      case MemberSingleLevel.diamond:
        return "Agen berlian";
      case MemberSingleLevel.partner:
        return "Agen Perak";
      case MemberSingleLevel.partnerSilver:
        return "Agen Perak";
      case MemberSingleLevel.partnerGold:
        return "Agen Emas";
      case MemberSingleLevel.partnerDiamond:
        return "Agen berlian";
    }
  }

  String fullDisplayName(BuildContext context) {
    switch(this) {
      case MemberSingleLevel.normalUser:
        return S.of(context).normal_member;
      case MemberSingleLevel.silver:
        return S.of(context).silver_agent;
      case MemberSingleLevel.gold:
        return S.of(context).gold_agent;
      case MemberSingleLevel.diamond:
        return S.of(context).diamond_agent;
      case MemberSingleLevel.partner:
        return S.of(context).partner;
      case MemberSingleLevel.partnerSilver:
        return S.of(context).silver_partner;
      case MemberSingleLevel.partnerGold:
        return S.of(context).gold_partner;
      case MemberSingleLevel.partnerDiamond:
        return S.of(context).diamond_partner;
    }
  }

  MemberSingleLevel nextLevel() {
    switch(this) {
      case MemberSingleLevel.normalUser:
        return MemberSingleLevel.silver;
      case MemberSingleLevel.silver:
        return MemberSingleLevel.gold;
      case MemberSingleLevel.gold:
        return MemberSingleLevel.diamond;
      case MemberSingleLevel.diamond:
        return MemberSingleLevel.partner;
      case MemberSingleLevel.partner:
        return MemberSingleLevel.partnerSilver;
      case MemberSingleLevel.partnerSilver:
        return MemberSingleLevel.partnerGold;
      case MemberSingleLevel.partnerGold:
        return MemberSingleLevel.partnerDiamond;
      case MemberSingleLevel.partnerDiamond:
        return MemberSingleLevel.partnerDiamond;
    }
  }

  String levelReward() {
    switch(this) {
      case MemberSingleLevel.normalUser:
        return "Rp0";
      case MemberSingleLevel.silver:
        return "Rp35.000";
      case MemberSingleLevel.gold:
        return "Rp300.000";
      case MemberSingleLevel.diamond:
        return "Rp10.000.000";
      case MemberSingleLevel.partner:
        return "Rp100.000";
      case MemberSingleLevel.partnerSilver:
        return "Rp100.000";
      case MemberSingleLevel.partnerGold:
        return "Rp2.000.000";
      case MemberSingleLevel.partnerDiamond:
        return "Rp100.000.000";
    }
  }

  int ratio() {
    switch(this) {
      case MemberSingleLevel.normalUser:
      case MemberSingleLevel.silver:
      case MemberSingleLevel.partnerSilver:
      case MemberSingleLevel.partner:
        return 1;
      case MemberSingleLevel.gold:
      case MemberSingleLevel.diamond:
      case MemberSingleLevel.partnerGold:
      case MemberSingleLevel.partnerDiamond:
        return 10;
    }
  }

  List<Color> displayColors() {
    switch(this) {
      case MemberSingleLevel.normalUser:
      case MemberSingleLevel.silver:
        return [Color(0xFF6B849D), Color(0xFF2E598E)];
      case MemberSingleLevel.gold:
        return [Color(0xFFB76928), Color(0xFF733C17)];
      case MemberSingleLevel.diamond:
        return [Color(0xFF6360AD), Color(0xFF3C2784)];
      case MemberSingleLevel.partner:
        return [Color(0xFF6B849D), Color(0xFF2E598E)];
      case MemberSingleLevel.partnerSilver:
        return [Color(0xFF6B849D), Color(0xFF2E598E)];
      case MemberSingleLevel.partnerGold:
        return [Color(0xFFB76928), Color(0xFF733C17)];
      case MemberSingleLevel.partnerDiamond:
        return [Color(0xFF6360AD), Color(0xFF3C2784)];
    }
  }

  List<Color> backgroundColors() {
    switch(this) {
      case MemberSingleLevel.normalUser:
      case MemberSingleLevel.silver:
      case MemberSingleLevel.gold:
      case MemberSingleLevel.diamond:
      case MemberSingleLevel.partnerSilver:
      case MemberSingleLevel.partnerGold:
      case MemberSingleLevel.partnerDiamond:
        return [Color(0xFF362C2C), Color(0xFF67605B)];
      case MemberSingleLevel.partner:
        return [Color(0xFFFFFEE8), Color(0xFFECC8AF)];
    }
  }

  String background() {
    switch(this) {
      case MemberSingleLevel.normalUser:
      case MemberSingleLevel.silver:
        return Assets.imagesBgAgentPerak;
      case MemberSingleLevel.gold:
        return  Assets.imagesBgAgenEmas;
      case MemberSingleLevel.diamond:
        return  Assets.imagesBgAgenBerlian;
      case MemberSingleLevel.partner:
        return Assets.imagesBgPartnersAgenPerak;
      case MemberSingleLevel.partnerSilver:
        return Assets.imagesBgPartnersAgenPerak;
      case MemberSingleLevel.partnerGold:
        return  Assets.imagesBgPartnersAgenEmas;
      case MemberSingleLevel.partnerDiamond:
        return  Assets.imagesBgPartnersAgenBerlian;
    }
  }

  List<MemberBenefits> memberBenefitsList(BuildContext context) {
    switch (this) {
      case MemberSingleLevel.normalUser:
      case MemberSingleLevel.silver:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
            S.of(context).training,
            S.of(context).training_detail,
            Assets.imagesIcMemberLevelBenefit3,
            false
          ),
          MemberBenefits(
            S.of(context).extra_cashback,
            S.of(context).extra_cashback_detail,
            Assets.imagesIcMemberLevelBenefit4,
            false
          )
        ];
      case MemberSingleLevel.gold:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail_gold,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
              S.of(context).training,
              S.of(context).extra_cashback_detail_gold,
              Assets.imagesIcMemberLevelBenefit3,
              false
          )
        ];
      case MemberSingleLevel.diamond:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail_gold,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
              S.of(context).training,
              S.of(context).extra_cashback_detail_gold,
              Assets.imagesIcMemberLevelBenefit3,
              false
          )
        ];
      case MemberSingleLevel.partner:
        return [
          MemberBenefits(
              S.of(context).member_benefits_partner_agent_1,
              S.of(context).member_benefits_partner_agent_1_value,
              R.assetsImagesIcMemberDuration,
              false
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_2,
            S.of(context).member_benefits_partner_agent_2_value,
            R.assetsImagesIcMemberBonus,
            false
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_3,
            S.of(context).member_benefits_partner_agent_3_value,
            R.assetsImagesIcMemberGroupBonus,
            false
          ),
          MemberBenefits(
            S.of(context).member_benefits_partner_agent_4,
            S.of(context).member_benefits_partner_agent_4_value,
            R.assetsImagesIcMemberExtraCashback,
            false
          ),
        ];
      case MemberSingleLevel.partnerSilver:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail2,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail_gold2,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
              S.of(context).extra_bonus,
              S.of(context).partner_extra_bonus1,
              Assets.imagesIcMemberLevelBenefit4,
              false
          )
        ];
      case MemberSingleLevel.partnerGold:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail2,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail_gold2,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
              S.of(context).extra_bonus,
              S.of(context).partner_extra_bonus2,
              Assets.imagesIcMemberLevelBenefit4,
              false
          )
        ];
      case MemberSingleLevel.partnerDiamond:
        return [
          MemberBenefits(
              S.of(context).direct_invite_reward,
              S.of(context).direct_invite_detail2,
              Assets.imagesIcMemberLevelBenefit1,
              false
          ),
          MemberBenefits(
            S.of(context).team_purchase_bonus,
            S.of(context).team_purchase_detail_gold2,
            Assets.imagesIcMemberLevelBenefit2,
            false,
          ),
          MemberBenefits(
              S.of(context).extra_bonus,
              S.of(context).partner_extra_bonus2,
              Assets.imagesIcMemberLevelBenefit4,
              false
          )
        ];
    }
  }
}