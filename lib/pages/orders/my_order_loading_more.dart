import 'dart:async';
import 'dart:math';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/models/order_item.dart';
import 'package:milestone/models/order_list_response.dart';
import 'package:milestone/models/order_status.dart';
import 'package:milestone/network/network_api_client.dart';

import '../../models/order_model.dart';

class OrderLoadingMoreAdapter extends LoadingMoreBase<OrderModel> {
  int _page = 1;
  final int _pageSize = 10;
  bool _hasMore = true;
  OrderStatus orderStatus;

  OrderLoadingMoreAdapter({required this.orderStatus});

  @override
  bool get hasMore => _hasMore;

  @override
  Future<bool> loadData([bool isLoadMoreAction = false]) async {
    await Future.delayed(Duration(seconds: 1)); // 模拟网络延迟

    if (!isLoadMoreAction) {
      _page = 1;
      clear();
    }

    OrderListResponse orderListResponse = await networkApiClient.getOrderList(page: _page, limit: _pageSize, type: orderStatus.type());

    if (orderListResponse.list.isEmpty || orderListResponse.list.length < orderListResponse.limit) {
      _hasMore = false;
    } else {
      _page++;
    }

    addAll(orderListResponse.list);
    return true;
  }
}
