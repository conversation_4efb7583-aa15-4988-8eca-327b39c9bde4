import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/order_status.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/image_indicator.dart';

import '../../utils/navigation_route.dart';
import 'my_order_list_view.dart';
import 'order_cashback_info_view.dart';

class MyOrderScreen extends StatefulWidget {
  const MyOrderScreen({super.key});

  @override
  State<MyOrderScreen> createState() => MyOrderScreenState();
}

class MyOrderScreenState extends State<MyOrderScreen> {
  double navigationPadding = 44;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          DefaultTabController(
            length: orderStatusList.length,
            child: Column(
              children: [
                AppBar(
                  elevation: 0,
                  backgroundColor: Colors.transparent,
                  centerTitle: true,
                  leading: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Image.asset(Assets.imagesIcArrowBack),
                  ),
                  title: Text(
                    S.of(context).order_title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: primaryTextColor,
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        customRouter(context, OrderCashbackInfoView());
                      },
                      child: Row(
                        children: [
                          Text(
                            S.of(context).order_cashback_info,
                            style: TextStyle(
                              color: secondaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(width: 2),
                          Image.asset(R.assetsImagesIcDetailQuestion, color: secondaryTextColor)
                        ],
                      )
                    ),
                  ],
                ),
                SizedBox(
                  height: navigationPadding,
                  child: buildNavigationTabBar(),
                ),
                Expanded(
                  child: TabBarView(
                    children: orderStatusList.map((status) {
                      return MyOrderListView(orderStatus: status);
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildNavigationTabBar() {
    return TabBar(
      isScrollable: false,
      dividerColor: Colors.transparent,
      indicatorSize: TabBarIndicatorSize.label,
      tabAlignment: TabAlignment.fill,
      labelColor: primaryTextColor,
      labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
      unselectedLabelColor: unSelectedTextColor,
      unselectedLabelStyle: TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
      ),
      indicator: ImageIndicator(),
      tabs: orderStatusList
          .map((e) => Tab(height: 30, text: e.displayName(context)))
          .toList(),
    );
  }
}
