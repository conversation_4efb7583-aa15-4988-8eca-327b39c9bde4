import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/vertical_dashed_divider.dart';

class OrderCashbackInfoView extends StatelessWidget {
  const OrderCashbackInfoView({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).rebate_instruction_title,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(16),
              child: Stack(
                children: [
                  Image.asset(
                    R.assetsImagesBgRebaseHintTop,
                    fit: BoxFit.cover,
                    width: MediaQuery.of(context).size.width,
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Image.asset(R.assetsImagesIcRebaseHintTop),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            S.of(context).rebate_instruction_content,
                            style: TextStyle(
                              fontSize: 12,
                              color: primaryTextColor,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),

            Padding(
              padding: EdgeInsets.all(16),
              child: buildStepContent(context),
            ),

            const SizedBox(height: 8),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Text(
                    S.of(context).rebate_how_to_get_title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  /// 如何下单
                  _buildInstructionCard(
                    title: S.of(context).rebate_how_to_order_title,
                    content: S.of(context).rebate_how_to_order_content,
                    iconName: R.assetsImagesIcRebaseCheck,
                    color: backgroundColor2,
                  ),

                  const SizedBox(height: 12),

                  /// 应用程序打开规则
                  _buildInstructionCard(
                    title: S.of(context).rebate_genco_last_app_title,
                    content: S.of(context).rebate_genco_last_app_content,
                    iconName: R.assetsImagesIcRebaseCheck,
                    color: backgroundColor2,
                  ),

                  const SizedBox(height: 12),

                  /// 不支持返现提示
                  _buildInstructionCard(
                    title: S.of(context).rebate_unsupported_order_title,
                    content: S.of(context).rebate_unsupported_order_content,
                    iconName: R.assetsImagesIcRebaseError,
                    color: backgroundColor2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildStepContent(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            _buildStepCircle("1", circleColor),
            const VerticalDashedDivider(height: 40),
            _buildStepCircle("2", circleColor),
            const VerticalDashedDivider(height: 40),
            _buildStepCircle("3", circleColor),
          ],
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            children: [
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow1,
                text: S.of(context).rebate_step_1_content,
                icons: R.assetsImagesIcTiktok14,
              ),
              const SizedBox(height: 12),
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow2,
                text: S.of(context).rebate_step_2_content,
                icons: R.assetsImagesIcLogo14,
              ),
              const SizedBox(height: 12),
              _buildStepCard(
                context,
                bgImage: R.assetsImagesBgRebaseCashbackFlow2,
                text: S.of(context).rebate_step_3_content,
                icons: R.assetsImagesIcLogo14,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStepCircle(String label, Color color) {
    return CircleAvatar(
      backgroundColor: color,
      radius: 16,
      child: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Widget _buildStepCard(
    BuildContext context, {
    required String bgImage,
    required String text,
    required String icons,
  }) {
    return Stack(
      children: [
        Image.asset(bgImage, fit: BoxFit.cover, width: double.infinity),
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Expanded(child: Text(text, style: const TextStyle(fontSize: 14))),
              const SizedBox(width: 8),
              Image.asset(icons),
            ],
          ),
        ),
      ],
    );
  }

  /// 卡片说明组件
  Widget _buildInstructionCard({
    required String title,
    required String content,
    required String iconName,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              children: [
                WidgetSpan(
                  child: Image.asset(iconName),
                  alignment: PlaceholderAlignment.middle,
                ),
                TextSpan(text: '  '),
                TextSpan(
                  text: title,
                  style: const TextStyle(
                    color: primaryTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(text: '  '),
                WidgetSpan(
                  child: Image.asset(Assets.imagesIcBrandHomeStar),
                  alignment: PlaceholderAlignment.top,
                ),
              ],
            ),
            textAlign: TextAlign.start,
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              color: secondaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
