import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

import '../../generated/assets.dart';
import '../../models/share_link_product_info.dart';
import '../../r.dart';
import '../../widget/loading_dialog.dart';

class SharePosterView extends StatefulWidget {
  const SharePosterView({super.key});

  @override
  State<StatefulWidget> createState() => SharePosterViewState();
}

class SharePosterViewState extends State<SharePosterView> {
  final GlobalKey _renderKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Wait for next frame if not ready
      await Future.delayed(const Duration(milliseconds: 100));
      startSharing();
    });
  }

  Future<void> startSharing() async {
    try {
      showLoadingDialog();
      // Capture the widget as an image
      final imageFile = await _captureWidgetImage();
      dismissLoadingDialog();
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(imageFile.path)],
          previewThumbnail: XFile(imageFile.path),
        ),
      );
    } catch (e) {
      Logger().d('Sharing error: $e');
      // Optionally show an error dialog/snackbar
    }
  }

  Future<File> _captureWidgetImage() async {
    final boundary = _renderKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    if (boundary == null) throw Exception('Render boundary not found');

    final image = await boundary.toImage(pixelRatio: 3.0);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final buffer = byteData!.buffer.asUint8List();

    final directory = await getTemporaryDirectory();
    final file = File('${directory.path}/share_poster.png');
    await file.writeAsBytes(buffer);
    return file;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          color: Theme.of(context).colorScheme.onSecondary,
          icon: Image.asset(R.assetsImagesIcDetailBackArrow),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          IconButton(
            onPressed: () async {
              await startSharing();
            },
            icon: Container(
              width: 29,
              height: 29,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(14),
              ),
              child: Image.asset(R.assetsImagesIcDetailShare),
            ),
          ),
        ],
      ),
      body: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            Assets.imagesBgSharePoster,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Center(
              child: RepaintBoundary(
                key: _renderKey,
                child: Stack(
                  children: [
                    Image.asset(
                      Assets.imagesBgSharePosterCover,
                      width: 322,
                      height: 578,
                      fit: BoxFit.cover,
                    ),
                    Positioned(
                      bottom: 86,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(6)),
                            ),
                            child: Text(
                              UserManager.getInviteCode() ?? "------",
                              style: TextStyle(
                                color: Color(0xFFE6AC44),
                                fontSize: 15,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    Positioned(
                      left: 10,
                      bottom: 5,
                      child: QrImageView(
                        data: 'https://genconusantara.com/s/${UserManager.getInviteCode()}',
                        version: QrVersions.auto,
                        size: 60.0,
                      ),
                    ),
                  ],
                ),
              )
          ),
        ],
      ),
    );
  }
}
