import 'package:azlistview/azlistview.dart';
import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/bank_list.dart';
import 'package:milestone/r.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/empty_view.dart';

class SelectBankListScreen extends StatefulWidget {
  const SelectBankListScreen({super.key});

  @override
  State<SelectBankListScreen> createState() => _SelectBankListScreenState();
}

class _SelectBankListScreenState extends State<SelectBankListScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<BankNameInfo> allBanks = [];
  List<BankNameInfo> filteredBanks = [];
  Set<String> existingLetters = {};
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initBankList();
  }

  // 初始化银行列表
  void _initBankList() {
    existingLetters.clear();
    allBanks.clear();

    // 构建所有银行数据
    for (String name in bankNames) {
      String firstChar = name.trim()[0].toUpperCase();
      String tagIndex = RegExp(r'^[A-Z]').hasMatch(firstChar) ? firstChar : '#';

      allBanks.add(BankNameInfo(name: name, tagIndex: tagIndex));
    }

    // 排序并设置初始分组状态
    SuspensionUtil.sortListBySuspensionTag(allBanks);
    SuspensionUtil.setShowSuspensionStatus(allBanks);
    filteredBanks = List.from(allBanks);

    // 初始索引数据
    _updateExistingLetters();
  }

  // 更新存在的字母集合
  void _updateExistingLetters() {
    existingLetters.clear();
    for (BankNameInfo bank in filteredBanks) {
      existingLetters.add(bank.getSuspensionTag());
    }
  }

  // 搜索过滤
  void _filterBanks(String query) {
    if (query.isEmpty) {
      filteredBanks = List.from(allBanks);
      _isSearching = false;
    } else {
      _isSearching = true;
      filteredBanks = allBanks.where((bank) {
        // 不区分大小写搜索
        return bank.name.toLowerCase().contains(query.toLowerCase());
      }).toList();

      // 搜索模式下重新排序
      SuspensionUtil.sortListBySuspensionTag(filteredBanks);
      SuspensionUtil.setShowSuspensionStatus(filteredBanks);
    }

    _updateExistingLetters();
    setState(() {});
  }

  // 构建银行列表项
  Widget _buildListItem(BankNameInfo info) {
    return ListTile(
      leading: Image.asset(R.assetsImagesIcMoneyBank),
      title: Text(
        info.name,
        style: const TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w400,
          color: primaryTextColor,
        ),
      ),
      onTap: () => Navigator.pop(context, info.name),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 8.0,
      ),
    );
  }

  // 构建分组头部
  Widget _buildSuspensionHeader(BankNameInfo info) {
    return Container(
      height: 20,
      margin: EdgeInsets.only(left: 8),
      alignment: Alignment.centerLeft,
      child: Text(
        info.getSuspensionTag(),
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: primaryTextColor,
        ),
      ),
    );
  }

  // 构建搜索框
  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: S.of(context).search,
            prefixIcon: Image.asset(R.assetsImagesIcSearch),
            suffixIcon: _isSearching
                ? IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () {
                      _searchController.clear();
                      _filterBanks('');
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12.0,
            ),
          ),
          onChanged: _filterBanks,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final indexBarData = existingLetters.toList()..sort();

    return BackgroundScaffold(
      title: S.of(context).select_bank,
      child: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: filteredBanks.isEmpty
                ? EmptyView()
                : Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    child: AzListView(
                      data: filteredBanks,
                      itemCount: filteredBanks.length,
                      itemBuilder: (context, index) {
                        final info = filteredBanks[index];
                        return _buildListItem(info);
                      },
                      susItemBuilder: (BuildContext context, int index) {
                        final info = filteredBanks[index];
                        return _buildSuspensionHeader(info);
                      },
                      physics: const ClampingScrollPhysics(),
                      indexBarData: indexBarData,
                      indexBarOptions: IndexBarOptions(
                        needRebuild: false,
                        textStyle: TextStyle(
                          fontSize: 12,
                          color: circleColor,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
