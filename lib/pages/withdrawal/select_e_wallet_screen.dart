import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/e_wallet.dart';
import 'package:milestone/r.dart';
import 'package:milestone/widget/background_scaffold.dart';

class SelectEWalletScreen extends StatefulWidget {
  const SelectEWalletScreen({super.key});

  @override
  State<SelectEWalletScreen> createState() => _SelectEWalletScreenState();
}

class _SelectEWalletScreenState extends State<SelectEWalletScreen> {
  int? _selectedIndex;

  final List<EWallet> _wallets = [
    EWallet('ShopeePay', R.assetsImagesIcShopeePay),
    EWallet('DANA', R.assetsImagesIcDanaPay),
    EWallet('OVO', R.assetsImagesIcOvoPay),
    EWallet('GoPay', R.assetsImagesIcGoPay),
  ];

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).select_e_wallet,
      child: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              itemCount: _wallets.length,
              separatorBuilder: (_, __) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final item = _wallets[index];
                final selected = _selectedIndex == index;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Image.asset(item.icon, width: 32, height: 32),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            item.name,
                            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                          ),
                        ),
                        selected
                            ? Image.asset(R.assetsImagesIcRebaseCheck, width: 20, height: 20)
                            : Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: const Color(0xFFD8D8D8), width: 1.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // 底部按钮
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 32),
            child: GestureDetector(
              onTap: () {
                if (_selectedIndex != null) {
                  final selectedWallet = _wallets[_selectedIndex!];
                  Navigator.pop(context, selectedWallet);
                }
              },
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFFC978), Color(0xFFFFB74D)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  S.of(context).button_next, // 假设翻译 key 是 continue_text
                  style: const TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}