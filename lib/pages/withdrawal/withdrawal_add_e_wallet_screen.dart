import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/models/e_wallet.dart';
import 'package:milestone/pages/withdrawal/select_e_wallet_screen.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';
import 'package:milestone/widget/bottom_info_view.dart';

import '../../utils/toast_utils.dart';

class WithdrawalAddEWalletScreen extends StatefulWidget {
  const WithdrawalAddEWalletScreen({super.key});

  @override
  State<WithdrawalAddEWalletScreen> createState() =>
      _WithdrawalAddEWalletScreenState();
}

class _WithdrawalAddEWalletScreenState
    extends State<WithdrawalAddEWalletScreen> {
  EWallet? selectedEWallet;
  String? name;
  String? eWalletAccount;

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: S.of(context).withdrawal_add_e_card,
      child: Column(
        children: [
          buildBankView(context),
          Spacer(),
          buildWithdrawalNextButton(context),
        ],
      ),
    );
  }

  Widget buildBankView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          SizedBox(height: 12),
          TextField(
            onChanged: (value) {
              setState(() {
                name = value;
              });
            },
            keyboardType: TextInputType.name,
            decoration: InputDecoration(
              hintText: S.of(context).name_placeholder,
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: secondaryTextColor,
              ),
            ),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          Divider(height: 24, thickness: 1, color: backgroundColor),
          InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => SelectEWalletScreen()),
              ).then((eWallet) {
                setState(() {
                  selectedEWallet = eWallet;
                });
              });
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).e_wallet,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: primaryTextColor,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    selectedEWallet != null
                        ? Row(
                            children: [
                              Image.asset(selectedEWallet!.icon),
                              SizedBox(width: 4),
                              Text(
                                selectedEWallet!.name,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: primaryTextColor,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            S.of(context).select_e_wallet,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: secondaryTextColor,
                            ),
                          ),
                    Spacer(),
                    Image.asset(Assets.imagesIcIncomeArrowRight),
                  ],
                ),
              ],
            ),
          ),
          Divider(height: 24, thickness: 1, color: backgroundColor),
          Text(
            S.of(context).phone_number,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Text(
                "+62 ",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: secondaryTextColor,
                ),
              ),
              Expanded(
                child: TextField(
                  onChanged: (value) {
                    setState(() {
                      eWalletAccount = value;
                    });
                  },
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: S.of(context).phone_number_placeholder,
                    border: InputBorder.none,
                    hintStyle: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: secondaryTextColor,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildWithdrawalNextButton(BuildContext context) {
    return TextButton(
      onPressed: () {
        if(name == null || name!.isEmpty) {
          makeToast(S.of(context).name_placeholder);
          return;
        }
        if(selectedEWallet == null) {
          makeToast(S.of(context).please_select_e_wallet);
          return;
        }

        if(eWalletAccount == null || eWalletAccount!.isEmpty) {
          makeToast(S.of(context).please_input_e_wallet_account);
          return;
        }

        showBottomInfoView(
          context,
          S.of(context).bind_bank_card_confirm,
          S.of(context).name,
          S.of(context).e_wallet,
          S.of(context).phone_number,
          name ?? "",
          selectedEWallet?.name ?? "",
          eWalletAccount ?? "",
          onButtonTapped: () async {
            Map<String, String> result = {
              "name": name ?? "",
              "bank_name": selectedEWallet?.name ?? "",
              "bank_card_number": eWalletAccount ?? "",
            };

            Navigator.pop(context, result);
          },
        );
      },
      child: Container(
        width: MediaQuery.of(context).size.width - 32,
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: selectedTabColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Center(
          child: Text(
            S.of(context).button_next,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
