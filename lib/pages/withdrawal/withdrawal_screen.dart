import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/pages/withdrawal/withdrawal_choose_screen.dart';
import 'package:milestone/pages/withdrawal/withdrawal_success_screen.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/string.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/widget/common_dialog.dart';

import '../../main.dart';
import '../../models/revenue_info.dart';
import '../../network/errors.dart';
import '../../utils/navigation_route.dart';

class WithdrawalScreen extends StatefulWidget {
  final RevenueInfo revenueInfo;
  const WithdrawalScreen({super.key, required this.revenueInfo});

  @override
  State<WithdrawalScreen> createState() => _WithdrawalScreenState();
}

class _WithdrawalScreenState extends State<WithdrawalScreen> {
  final TextEditingController textEditController = TextEditingController();
  double tips = 0;
  Map<String, String>? bankInfo;

  @override
  void dispose() {
    textEditController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Stack(
        children: [
          Image.asset(
            Assets.imagesBgCommon,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Column(
            children: [
              AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                centerTitle: true,
                leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Image.asset(Assets.imagesIcArrowBack),
                ),
                title: Text(
                  S.of(context).income_withdrawal_button,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: primaryTextColor,
                  ),
                ),
              ),

              buildAccountView(context),
              buildWithdrawalAmountView(context),
              Spacer(),
              buildWithdrawalButton(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildAccountView(BuildContext context) {
    return InkWell(
      onTap: () async {
        Map<String, String> result = await customRouter(context, WithdrawalChooseScreen());
        setState(() {
          bankInfo = result;
        });
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 1,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).withdrawal_account,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: primaryTextColor,
              ),
            ),
            SizedBox(height: 12),
            Row(
              children: [
                buildSelectedBankInfoView(context),
                Spacer(),
                Image.asset(Assets.imagesIcIncomeArrowRight),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildSelectedBankInfoView(BuildContext context) {
    if (bankInfo != null) {
      return ConstrainedBox(
        // 添加宽度约束
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8, // 限制最大宽度
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 银行名称（确保自动换行）
            Text(
              bankInfo?["bank_name"] ?? "",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: primaryTextColor,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            // 银行卡号
            Text(
              bankInfo?["bank_card_number"]?.toMaskedBankCard() ?? "",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: secondaryTextColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      );
    } else {
      return ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        child: Text(
          S.of(context).please_select_withdrawal_account,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: secondaryTextColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      );
    }
  }

  Widget buildWithdrawalAmountView(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 1,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).withdrawal_amount,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: primaryTextColor,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Text(
                "Rp",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: primaryTextColor,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: textEditController,
                  onChanged: (value) {
                    try {
                      double withdrawableAmount = double.parse(value);
                      double tips = withdrawableAmount * 0.15;
                      setState(() {
                        this.tips = tips;
                      });
                    } catch (e) {
                      makeToast(e.toString());
                    }
                  },
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "${S.of(context).withdrawal_amount_hint}Rp${widget.revenueInfo.withdrawableAmount}",
                    border: InputBorder.none,
                    hintStyle: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: secondaryTextColor,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          Divider(height: 1, thickness: 1, color: backgroundColor, indent: 5.0),
          Row(
            children: [
              Text(
                "${S.of(context).withdrawal_amount_min}:Rp${Global.rechargeConfig?.minWithdrawAmount}",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: secondaryTextColor,
                ),
              ),
              Spacer(),
              TextButton(
                onPressed: () {
                  try {
                    double withdrawableAmount = double.parse(widget.revenueInfo.withdrawableAmount);
                    double tips = withdrawableAmount * 0.15;
                    setState(() {
                      this.tips = tips;
                    });
                    textEditController.text = widget.revenueInfo.withdrawableAmount;
                  } catch (e) {
                    makeToast(e.toString());
                  }
                },
                child: Text(
                  S.of(context).withdrawal_all,
                  style: TextStyle(
                    color: selectedTabColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              showCustomDialog(
                context: context,
                title: S.of(context).withdrawal_fees,
                description: S.of(context).withdrawal_fees_hint,
                confirmButtonText: S.of(context).ok,
                onConfirm: (BuildContext dialogContext) {
                  Navigator.pop(dialogContext);
                },
              );
            },
            child: Row(
              children: [
                Text(
                  S.of(context).withdrawal_fees,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: primaryTextColor,
                  ),
                ),
                SizedBox(width: 4),
                Image.asset(Assets.imagesIcDetailQuestion),
                Spacer(),
                Text(
                  "Rp${tips.toStringAsFixed(0)}",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8),
          Divider(height: 1, thickness: 1, color: backgroundColor, indent: 5.0),
          SizedBox(height: 8),
          Text(
            S.of(context).withdrawal_hint,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: secondaryTextColor,
            ),
          ),
          Text(
            S.of(context).withdrawal_hint_description,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildWithdrawalButton(BuildContext context) {
    return TextButton(
      onPressed: () async {
        if(bankInfo == null) {
          makeToast(S.of(context).please_select_bank_or_e_wallet);
          return;
        }
        if(textEditController.text.isEmpty) {
          makeToast(S.of(context).please_input_amount);
          return;
        }
        String name = bankInfo!["name"] ?? "";
        String extractType = bankInfo!["type"] ?? "";
        String bankCode = bankInfo!["bank_card_number"] ?? "";
        String bankName = bankInfo!["bank_name"] ?? "";
        String amount = textEditController.text ?? "";
        String mark = "";

        try {
          await networkApiClient.extractCash(name, extractType, bankCode, bankName, amount, mark);
          if(context.mounted) {
            customRouter(context, WithdrawalSuccessScreen());
          }
        } catch (e) {
          if(context.mounted) {
            ApiErrorHandler.handleError(e, context);
          }
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width - 32,
        margin: EdgeInsets.only(left: 16, right: 16, bottom: 16 + MediaQuery.of(context).padding.bottom),
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: selectedTabColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Center(
          child: Text(
            S.of(context).income_withdrawal_button,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
