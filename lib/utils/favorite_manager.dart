class FavoriteManager {
  static final FavoriteManager _instance = FavoriteManager._internal();
  factory FavoriteManager() => _instance;

  FavoriteManager._internal();

  final Set<int> _favorites = {};

  bool isFavorite(int id) => _favorites.contains(id);

  void toggleFavorite(int id) {
    if (isFavorite(id)) {
      _favorites.remove(id);
    } else {
      _favorites.add(id);
    }
  }

  Set<int> get favorites => Set.unmodifiable(_favorites);
}