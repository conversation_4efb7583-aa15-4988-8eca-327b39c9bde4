import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:milestone/utils/sp.dart';
import 'package:logger/logger.dart';


class LanguageManager with ChangeNotifier {
  Locale _locale = PlatformDispatcher.instance.locale;

  Locale get locale => _locale;

  LanguageManager() {
    String language = SpUtil.getString("language");
    if (language.isNotEmpty) {
      _locale = Locale(language);
    }
    updateNativeLanguage(_locale);
  }

  void setLocale(Locale newLocale) {
    _locale = newLocale;
    notifyListeners();
    updateNativeLanguage(_locale);
    SpUtil.putString("language", newLocale.languageCode);
  }

  Future<void> updateNativeLanguage(Locale locale) async {
    // try {
    //   MethodChannel channel = const MethodChannel('channel:action');
    //   await channel.invokeMethod('language', {"language": locale.languageCode});
    // } on PlatformException catch (e) {
    //   Logger().e("Error: ${e.message}");
    // }
  }
}
