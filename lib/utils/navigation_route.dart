import 'dart:io';

import 'package:flutter/cupertino.dart';

import 'cartoon.dart';

enum CartoonRouter {
  customRouteFade,
  customRouteZoom,
  customRouteRotateZoom,
  customRouteSlide,
  customUpRouteSlide,
}

/// 路由跳转方法
/// cartoonRouter只有安卓生效，ios只能调用CupertinoPageRoute。目的是保留IOS侧滑返回功能
/// https://github.com/flutter/flutter/issues/47441 这个issue如果close的话IOS也可能添加过度动画
Future customRouter(
  BuildContext context,
  Widget widget, {
  CartoonRouter cartoonRouter = CartoonRouter.customRouteSlide,
}) {
  PageRouteBuilder router;
  switch (cartoonRouter) {
    case CartoonRouter.customRouteFade:
      router = CustomRouteFade(widget);
      break;
    case CartoonRouter.customRouteZoom:
      router = CustomRouteZoom(widget);
      break;
    case CartoonRouter.customRouteRotateZoom:
      router = CustomRouteRotateZoom(widget);
      break;
    case CartoonRouter.customRouteSlide:
      router = CustomRouteSlide(widget);
      break;
    case CartoonRouter.customUpRouteSlide:
      router = CustomUpRouteSlide(widget);
      break;
  }
  PageRoute routeBuilder = Platform.isIOS
      ? CupertinoPageRoute(builder: (context) => widget)
      : router;
  return Navigator.push(context, routeBuilder);
}
