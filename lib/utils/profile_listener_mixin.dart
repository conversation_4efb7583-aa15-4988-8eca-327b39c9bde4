import 'package:flutter/cupertino.dart';
import 'package:milestone/pages/member/member_level.dart';
import 'package:milestone/pages/member/member_single_level.dart';
import 'package:milestone/utils/user_utils.dart';

import '../controller/event_bus_controller.dart';

mixin ProfileListenerMixin<T extends StatefulWidget> on State<T> {
  late String nickname;
  late String avatarUrl;
  late int memberLevelValue;
  late MemberLevel memberLevel;
  late MemberSingleLevel memberSingleLevel;
  late String inviteCode;
  late int integral;
  int? spreadUid;
  String? spreadNickName;

  @override
  void initState() {
    nickname = UserManager.getNickname() ?? "";
    avatarUrl = UserManager.getAvatar() ?? "";
    memberLevelValue = UserManager.getMemberLevelValue();
    memberLevel = UserManager.getMemberLevel();
    memberSingleLevel = UserManager.getMemberSingleLevel();
    inviteCode = UserManager.getInviteCode() ?? "------";
    integral = UserManager.getIntegral();
    spreadUid = UserManager.getFullUserInfo()?.spreadUid;
    spreadNickName = UserManager.getFullUserInfo()?.spreadNickName;
    eventBus.on<ProfileUpdateEvent>().listen((event) {
      setState(() {
        nickname = event.username;
        if (event.avatarUrl != null) {
          avatarUrl = event.avatarUrl!;
        }
        spreadUid = UserManager.getFullUserInfo()?.spreadUid;
        spreadNickName = UserManager.getFullUserInfo()?.spreadNickName;
        if (event.memberLevel != null) {
          memberLevelValue = event.memberLevel!;
          switch(memberLevelValue) {
            case 0: memberSingleLevel =  MemberSingleLevel.normalUser;
            case 1: memberSingleLevel =  MemberSingleLevel.silver;
            case 2: memberSingleLevel =  MemberSingleLevel.gold;
            case 3: memberSingleLevel =  MemberSingleLevel.diamond;
            case 4: memberSingleLevel =  MemberSingleLevel.partnerSilver;
            case 5: memberSingleLevel =  MemberSingleLevel.partnerGold;
            case 6: memberSingleLevel =  MemberSingleLevel.partnerDiamond;
            default: memberSingleLevel =  MemberSingleLevel.normalUser;
          }
          if(memberLevelValue <= 0) {
            memberLevel = MemberLevel.normal;
          } else if(memberLevelValue <= 3) {
            memberLevel = MemberLevel.silverAgent;
          }else {
            memberLevel = MemberLevel.partners;
          }
        }
      });
    });
    super.initState();
  }
}
