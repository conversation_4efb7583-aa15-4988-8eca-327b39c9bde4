import 'dart:math' as math;
import 'dart:math';
import 'dart:convert';

extension BankCardFormatter on String {
  /// 格式化银行卡号，将中间四位替换为星号
  String toMaskedBankCard() {
    if (isEmpty) return "";

    final length = this.length;

    if (length <= 4) {
      // 短于4位直接返回
      return this;
    } else if (length == 5) {
      // 5位：隐藏中间3位
      return '${substring(0, 1)}***${substring(4)}';
    } else if (length == 6) {
      // 6位：隐藏中间4位
      return '${substring(0, 1)}****${substring(5)}';
    } else {
      // 7位及以上：隐藏中间4位
      final prefix = substring(0, (length - 4) ~/ 2);
      final suffix = substring((length - 4) ~/ 2 + 4);
      return '$prefix****$suffix';
    }
  }
}



class RandomStringGenerator {
  static final Random _random = Random.secure();
  static const String _defaultChars =
      'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';

  /// 生成随机字符串
  ///
  /// [length] - 字符串长度 (默认 16)
  /// [chars] - 可选字符集 (默认大小写字母+数字)
  static String generate({
    int length = 16,
    String? chars,
  }) {
    final codeUnits = List.generate(
      length,
          (index) {
        final charSet = chars ?? _defaultChars;
        return charSet.codeUnitAt(_random.nextInt(charSet.length));
      },
    );
    return String.fromCharCodes(codeUnits);
  }

  /// 生成加密安全的随机字符串 (基于字节)
  ///
  /// [length] - 字符串长度 (默认 32)
  static String generateSecure({int length = 32}) {
    final bytes = List<int>.generate(
        length,
            (i) => _random.nextInt(256)
    );
    return base64Url.encode(bytes).replaceAll('=', '').substring(0, length);
  }

  /// 生成指定类型的随机字符串
  ///
  /// [type] - 字符串类型：
  ///   'alpha': 仅字母
  ///   'numeric': 仅数字
  ///   'alphanum': 字母+数字 (默认)
  ///   'lower': 仅小写字母
  ///   'upper': 仅大写字母
  static String generateTyped({
    int length = 16,
    String type = 'alphanum',
  }) {
    final Map<String, String> charSets = {
      'alpha': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
      'numeric': '0123456789',
      'alphanum': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
      'lower': 'abcdefghijklmnopqrstuvwxyz',
      'upper': 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      'hex': '0123456789abcdef',
    };

    final selectedSet = charSets[type] ?? charSets['alphanum']!;
    return generate(length: length, chars: selectedSet);
  }
}

extension IDRFormatting on String {
  String formatIDR() {
    // 处理小数部分四舍五入逻辑
    String roundedInteger = this;
    if (contains('.')) {
      try {
        // 尝试解析为double进行四舍五入
        double? parsed = double.tryParse(this);
        if (parsed != null) {
          // 四舍五入后转整数
          roundedInteger = parsed.round().toString();
        } else {
          // 解析失败时使用字符串操作四舍五入
          List<String> parts = this.split('.');
          String integerPart = parts[0];
          String decimalPart = parts.length > 1 ? parts[1] : '0';

          // 处理负号
          bool isNegative = integerPart.startsWith('-');
          integerPart = integerPart.replaceAll('-', '');

          // 检查是否需要进位
          bool needRoundUp = decimalPart.isNotEmpty &&
              int.tryParse(decimalPart.substring(0, 1)) != null &&
              int.parse(decimalPart.substring(0, 1)) >= 5;

          // 字符串进位函数
          String roundUp(String number) {
            final digits = number.split('').reversed.map((d) => int.parse(d)).toList();
            int carry = 1;
            for (int i = 0; i < digits.length && carry > 0; i++) {
              digits[i] += carry;
              carry = digits[i] ~/ 10;
              digits[i] %= 10;
            }
            if (carry > 0) digits.add(carry);
            return digits.reversed.map((d) => d.toString()).join();
          }

          if (needRoundUp) {
            integerPart = integerPart.isEmpty ? '1' : roundUp(integerPart);
          }

          // 处理负号
          if (isNegative) {
            if (integerPart == '0') {
              // 负0转为0
              integerPart = '0';
            } else {
              integerPart = '-$integerPart';
            }
          }
          roundedInteger = integerPart;
        }
      } catch (e) {
        // 发生错误时使用原始字符串
        roundedInteger = this;
      }
    }

    // 使用新字符串继续原有逻辑
    String numStr = roundedInteger;

    // 清理字符串：移除所有非数字字符（保留开头的负号）
    numStr = numStr.replaceAll(RegExp(r'[^0-9-]'), '');

    // 处理负号（只允许开头有一个负号）
    final bool hasNegative = numStr.startsWith('-');
    final String digits = hasNegative ? numStr.substring(1) : numStr;
    numStr = hasNegative ? '-${digits.replaceAll('-', '')}' : digits;

    // 空值检查
    if (numStr.isEmpty || numStr == '-') return '0';

    // 处理负号
    final bool isNegative = numStr.startsWith('-');
    String unsignedStr = isNegative ? numStr.substring(1) : numStr;

    // 处理全零情况
    if (unsignedStr.replaceAll('0', '').isEmpty) return '0';

    // 移除前导零（保留最后一位）
    unsignedStr = unsignedStr.replaceFirst(RegExp(r'^0+'), '');
    if (unsignedStr.isEmpty) unsignedStr = '0';

    // 添加千位分隔符
    final StringBuffer result = StringBuffer();
    final int length = unsignedStr.length;

    for (int i = 0; i < length; i++) {
      final int digitIndex = length - i - 1;
      result.write(unsignedStr[i]);

      // 从右向左每3位添加分隔符（排除最后一位）
      if ((digitIndex) % 3 == 0 && digitIndex != 0) {
        result.write('.');
      }
    }

    // 添加负号
    return '${isNegative ? '-' : ''}${result.toString()}';
  }
}

extension IDRFormattingNum on num {
  String formatIDR() {
    if (this == null) return '0';

    // 转换为整数
    final intValue = toInt();
    final isNegative = intValue < 0;
    final unsignedValue = intValue.abs().toString();

    // 处理零值
    if (unsignedValue == '0') return '0';

    // 添加千位分隔符
    final length = unsignedValue.length;
    final buffer = StringBuffer();

    for (var i = 0; i < length; i++) {
      buffer.write(unsignedValue[i]);

      // 每 3 位添加分隔符（排除最后一位）
      if ((length - i - 1) > 0 && (length - i - 1) % 3 == 0) {
        buffer.write('.');
      }
    }

    return '${isNegative ? '-' : ''}${buffer.toString()}';
  }
}

String formatThousandsSeparator(dynamic input) {
  // 处理 null 和空输入
  if (input == null) return '0';

  String numStr;
  if (input is String) {
    // 清理字符串：移除所有非数字字符（保留负号）
    numStr = input.replaceAll(RegExp(r'[^0-9-]'), '');

    // 处理负号（只允许开头有一个负号）
    final bool hasNegative = numStr.startsWith('-');
    final String digits = hasNegative ? numStr.substring(1) : numStr;
    numStr = hasNegative ? '-${digits.replaceAll('-', '')}' : digits;

    // 空值检查
    if (numStr.isEmpty || numStr == '-') return '0';
  } else if (input is num) {
    // 处理数字类型：先取整，再转字符串
    numStr = input.toInt().toString();
  } else {
    return '0';
  }

  // 处理负号
  final bool isNegative = numStr.startsWith('-');
  String unsignedStr = isNegative ? numStr.substring(1) : numStr;

  // 处理全零情况
  if (unsignedStr.replaceAll('0', '').isEmpty) return '0';

  // 移除前导零（保留最后一位）
  unsignedStr = unsignedStr.replaceFirst(RegExp(r'^0+'), '');
  if (unsignedStr.isEmpty) unsignedStr = '0';

  // 添加千位分隔符
  final StringBuffer result = StringBuffer();
  final int length = unsignedStr.length;

  for (int i = 0; i < length; i++) {
    final int digitIndex = length - i - 1;
    result.write(unsignedStr[i]);

    // 从右向左每 3 位添加分隔符（排除最后一位）
    if ((digitIndex) % 3 == 0 && digitIndex != 0) {
      result.write('.');
    }
  }

  // 添加负号
  return '${isNegative ? '-' : ''}${result.toString()}';
}