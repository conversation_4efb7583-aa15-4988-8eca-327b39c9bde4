import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:milestone/utils/user_utils.dart';
import 'package:tiktok_sdk_v2/tiktok_sdk_v2.dart';
import '../models/user_info.dart';
import '../network/network_api_client.dart';

String getCallbackUrl() {
  if(Platform.isAndroid) {
    return 'https://genconusantara.com/callback';
  }else {
    return 'https://genconusantara.com/auth/callback';
  }
}

class TiktokLogin {
  static const String clientKey = 'awnb2jrqphg8q3x9';
  static const String clientSecret = 'THUgLUinHl6ymTkeYa3RQqUEcmQVa4ox';
  static const String redirectUri = 'https://genconusantara.com/auth/callback';
  static const String androidRedirectUrl = 'https://genconusantara.com/callback';


  static Future<void> initTikTokLogin() async {
    final MethodChannel resultChannel = MethodChannel('channel:tiktokLoginResult');
    resultChannel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'sendingLoginResult':
          Logger().d("sendDeviceToken call.arguments = ${call.arguments}");
          final dynamic result = call.arguments;
          if (result != null) {
            TikTokLoginResult loginResult = TikTokLoginResult(
              status: result["authCode"] != null
                  ? TikTokLoginStatus.success
                  : TikTokLoginStatus.error,
              authCode:
              result["authCode"] != null ? result["authCode"] as String : "",
              codeVerifier: result["codeVerifier"] as String,
              state: result["state"] as String?,
            );
            try {
              Response loginResponse = (await networkApiClient.thirdPartyLoginLogin(
                code: result["authCode"],
                codeVerifier: result["codeVerifier"],
              ));
              if (loginResponse.statusCode == 200) {
                final Map<String, dynamic> responseBody = loginResponse.data is Map
                    ? loginResponse.data as Map<String, dynamic>
                    : json.decode(loginResponse.data);
                if (responseBody['code'] == 200) {
                  UserManager.saveUserInfo(responseBody["data"]);
                  UserInfo userInfo = await networkApiClient.getUserInfo();
                  UserManager.saveFullUserInfo(userInfo);
                  return;
                }
              }
            } on Exception catch (e) {
              Logger().e("TikTok login error: $e");
            }
          }
      }
    });
  }

  // Add any additional methods or properties needed for TikTok login
  static Future<TikTokLoginResult> login() async {
    if(Platform.isAndroid) {
      try {
        MethodChannel channel = const MethodChannel('channel:tiktokLogin');
        final dynamic result = await channel.invokeMethod('login', {
          'clientKey': clientKey,
          'clientSecret': clientSecret,
          'redirectUri': redirectUri,
        });

        if (result != null) {
          return TikTokLoginResult(
            status: result["authCode"] != null
                ? TikTokLoginStatus.success
                : TikTokLoginStatus.error,
            authCode:
            result["authCode"] != null ? result["authCode"] as String : "",
            codeVerifier: result["codeVerifier"] as String,
            state: result["state"] as String?,
          );
        } else {
          return const TikTokLoginResult(
            status: TikTokLoginStatus.error,
          );
        }
      } on PlatformException catch (e) {
        Logger().e("Failed to login: ${e.message}");
        return Future.error("TikTok login failed: ${e.message}");
      }
    } else {
      final result = await TikTokSDK.instance.login(permissions: {
        TikTokPermissionType.userInfoBasic,
      }, redirectUri: redirectUri);
      Logger().d("tiktok login result:$result");
      return result;
    }
  }
}