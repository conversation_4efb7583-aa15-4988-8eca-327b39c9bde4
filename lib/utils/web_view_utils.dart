// import 'package:flutter/foundation.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
//
// Future<void> openWebView(String url) async {
//   final browser = InAppBrowser();
//   final settings = InAppBrowserClassSettings(
//     browserSettings: InAppBrowserSettings(hideUrlBar: true),
//     webViewSettings: InAppWebViewSettings(
//       javaScriptEnabled: true,
//       isInspectable: kDebugMode,
//     ),
//   );
//   browser.openUrlRequest(
//     urlRequest: URLRequest(url: WebUri(url)),
//     settings: settings,
//   );
// }
