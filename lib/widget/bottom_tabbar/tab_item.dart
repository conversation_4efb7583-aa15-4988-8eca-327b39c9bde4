import 'package:flutter/cupertino.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';

enum HomeTabItem { home, brand, income, mine }

List<HomeTabItem> homeTabItems = [
  HomeTabItem.home,
  HomeTabItem.brand,
  HomeTabItem.income,
  HomeTabItem.mine
];

extension HomeTabItemDisplayName on HomeTabItem {
  String displayName(BuildContext context) {
    switch (this) {
      case HomeTabItem.home:
        return S.of(context).home_navigation_home;
      case HomeTabItem.brand:
        return S.of(context).home_navigation_brand;
      case HomeTabItem.income:
        return S.of(context).home_navigation_income;
      case HomeTabItem.mine:
        return S.of(context).home_navigation_mine;
    }
  }

  Widget selectIcon(BuildContext context) {
    switch (this) {
      case HomeTabItem.home:
        return Image.asset(Assets.imagesIcHomeTabSelected);
      case HomeTabItem.brand:
        return Image.asset(Assets.imagesIcBrandTabSelected);
      case HomeTabItem.income:
        return Image.asset(Assets.imagesIcIncomeTabSelected);
      case HomeTabItem.mine:
        return Image.asset(Assets.imagesIcMeTabSelected);
    }
  }

  Widget get icon {
    switch (this) {
      case HomeTabItem.home:
        return Image.asset(Assets.imagesIcHomeTab);
      case HomeTabItem.brand:
        return Image.asset(Assets.imagesIcBrandTab);
      case HomeTabItem.income:
        return Image.asset(Assets.imagesIcIncomeTab);
      case HomeTabItem.mine:
        return Image.asset(Assets.imagesIcMeTab);
    }
  }
}