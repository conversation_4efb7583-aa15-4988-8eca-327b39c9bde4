import 'package:flutter/material.dart';

enum CustomTabAlignment { start, middle }

class CustomLineTabIndicator extends Decoration {
  /// Create an underline style selected tab indicator.
  ///
  /// The [borderSide] and [insets] arguments must not be null.
  const CustomLineTabIndicator(
      {this.borderSide = const BorderSide(width: 2.0, color: Colors.white),
      this.insets = EdgeInsets.zero,
      this.alignment = CustomTabAlignment.start,
      this.width,
      this.radius,
      this.color,
      this.boxShadow,
      this.close = true});

  /// The color and weight of the horizontal line drawn below the selected tab.
  final BorderSide borderSide;

  final double? radius;

  final double? width;

  final Color? color;

  final CustomTabAlignment alignment;

  final bool close;

  final List<BoxShadow>? boxShadow;

  /// Locates the selected tab's underline relative to the tab's boundary.
  ///
  /// The [TabBar.indicatorSize] property can be used to define the
  /// tab indicator's bounds in terms of its (centered) tab widget with
  /// [TabIndicatorSize.label], or the entire tab with [TabIndicatorSize.tab].
  final EdgeInsetsGeometry insets;

  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is CustomLineTabIndicator) {
      return CustomLineTabIndicator(
        borderSide: BorderSide.lerp(a.borderSide, borderSide, t),
        insets: EdgeInsetsGeometry.lerp(a.insets, insets, t)!,
      );
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is CustomLineTabIndicator) {
      return CustomLineTabIndicator(
        borderSide: BorderSide.lerp(borderSide, b.borderSide, t),
        insets: EdgeInsetsGeometry.lerp(insets, b.insets, t)!,
      );
    }
    return super.lerpTo(b, t);
  }

  @override
  UnderlinePainter createBoxPainter([VoidCallback? onChanged]) {
    return UnderlinePainter(this, alignment, onChanged, close, boxShadow);
  }
}

class UnderlinePainter extends BoxPainter {
  UnderlinePainter(
      this.decoration, this.alignment, VoidCallback? onChanged, this.close, this.boxShadow)
      : super(onChanged);

  final CustomLineTabIndicator decoration;
  final CustomTabAlignment alignment;
  final bool close;
  final List<BoxShadow>? boxShadow;

  BorderSide get borderSide => decoration.borderSide;
  EdgeInsetsGeometry get insets => decoration.insets;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration.size != null);

    final Paint paint = Paint();
    final double indicatorWidth = decoration.width ?? configuration.size!.width;
    Offset localOffset = const Offset(0, 30);

    if (alignment == CustomTabAlignment.middle) {
      double startX = (configuration.size!.width - indicatorWidth) / 2;
      localOffset = close ? Offset(startX, configuration.size!.height - 4) : Offset(startX, 30);
    }

    final Rect underlineRect = offset + localOffset & Size(indicatorWidth, 4);
    final RRect underlineRRect = RRect.fromRectAndRadius(underlineRect, Radius.circular(decoration.radius ?? 0));

    // 绘制下划线
    paint
      ..style = PaintingStyle.fill
      ..color = decoration.color ?? borderSide.color;
    canvas.drawRRect(underlineRRect, paint);

    // 绘制阴影
    if (boxShadow != null) {
      for (final BoxShadow shadow in boxShadow!) {
        final Path shadowPath = Path()
          ..addRRect(underlineRRect)
          ..transform(Matrix4.translationValues(shadow.offset.dx, shadow.offset.dy, 0.0).storage);
        paint
          ..color = shadow.color
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadow.blurRadius);
        canvas.drawPath(shadowPath, paint);
      }
    }
  }
}
