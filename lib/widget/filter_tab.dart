import 'package:flutter/material.dart';
import 'package:milestone/models/transaction_detail_filter.dart';
import 'package:milestone/themes/colors.dart';

class FilterTab extends StatefulWidget {
  final TransactionDetailFilter filter;
  final TabController controller;
  final int index;

  const FilterTab({
    super.key,
    required this.filter,
    required this.controller,
    required this.index,
  });

  @override
  State<FilterTab> createState() => _FilterTabState();
}

class _FilterTabState extends State<FilterTab> {
  late bool _isSelected;

  @override
  void initState() {
    super.initState();
    _isSelected = widget.controller.index == widget.index;
    widget.controller.addListener(_updateSelection);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_updateSelection);
    super.dispose();
  }

  void _updateSelection() {
    setState(() {
      _isSelected = widget.controller.index == widget.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Tab(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.only(
          left: 10,
          top: 5,
          right: 10,
          bottom: 5,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              _isSelected
                  ? widget.filter.selectedImage(context)
                  : widget.filter.image(context),
              width: 20,
              height: 20,
              color: _isSelected ? Colors.white : unSelectedTextColor,
            ),
            const SizedBox(width: 5),
            Text(widget.filter.displayName(context))
          ],
        ),
      ),
    );
  }
}