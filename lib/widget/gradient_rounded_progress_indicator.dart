import 'package:flutter/material.dart';

class GradientRoundedProgressIndicator extends StatelessWidget {
  final double value;
  final Gradient gradient;

  const GradientRoundedProgressIndicator({
    super.key,
    required this.value,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(3),
      child: SizedBox(
        height: 3,
        child: Stack(
          children: [
            // 背景层（灰色底）
            Container(color: Colors.white),
            // 进度条层（渐变）
            FractionallySizedBox(
              widthFactor: value,
              child: Container(
                decoration: BoxDecoration(gradient: gradient),
              ),
            ),
          ],
        ),
      ),
    );
  }
}