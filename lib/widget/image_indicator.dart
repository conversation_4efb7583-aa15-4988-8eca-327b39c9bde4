import 'dart:ui' as ui;

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:milestone/r.dart';

class ImageIndicator extends Decoration {
  const ImageIndicator();

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ImageIndicatorPainter(onChanged);
  }
}

class _ImageIndicatorPainter extends BoxPainter {
  _ImageIndicatorPainter(super.onChanged);

  static ui.Image? _indicatorImage;
  static bool _isLoading = false;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final rect = offset & configuration.size!;
    final devicePixelRatio = WidgetsBinding.instance.window.devicePixelRatio;

    if (_indicatorImage != null) {
      _paintImage(canvas, rect, _indicatorImage!, devicePixelRatio);
    } else if (!_isLoading) {
      _isLoading = true;
      _loadIndicatorImage().then((image) {
        _indicatorImage = image;
        _isLoading = false;
        onChanged?.call();
      });
    }
  }

  void _paintImage(Canvas canvas, Rect rect, ui.Image image, double devicePixelRatio) {
    const targetLogicalHeight = 8.0;
    final aspectRatio = image.width / image.height;
    final targetLogicalWidth = targetLogicalHeight * aspectRatio;

    final centerX = rect.center.dx;
    final bottomY = rect.bottom;
    final x = centerX - targetLogicalWidth / 2;
    final y = bottomY - targetLogicalHeight;

    final dstRect = Rect.fromLTWH(x, y, targetLogicalWidth, targetLogicalHeight);
    final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());

    // 关键修改：使用高质量缩放
    canvas.drawImageRect(
      image,
      srcRect,
      dstRect,
      Paint()
        ..filterQuality = FilterQuality.high
        ..isAntiAlias = true,
    );
  }

  Future<ui.Image?> _loadIndicatorImage() async {
    try {
      final data = await rootBundle.load(R.assetsImagesIcHomeTabIndicator);
      return _decodeImage(data);
    } catch (e) {
      debugPrint("Error loading indicator image: $e");
      return null;
    }
  }

  Future<ui.Image> _decodeImage(ByteData data) async {
    // 直接解码原始图片
    final codec = await ui.instantiateImageCodec(
      data.buffer.asUint8List(),
    );
    final frame = await codec.getNextFrame();
    return frame.image;
  }
}