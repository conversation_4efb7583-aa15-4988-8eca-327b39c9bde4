import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/models/bind_invite_code_response.dart';
import 'package:milestone/network/network_api_client.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/toast_utils.dart';
import 'package:milestone/widget/loading_dialog.dart';

import '../generated/l10n.dart';
import '../network/errors.dart';
import '../r.dart';
import 'invite_group_information_bottom_view.dart';

class BottomInputInviteCodeView extends StatelessWidget {
  final VoidCallback? onButtonTapped;
  final TextEditingController textEditingController = TextEditingController();

  BottomInputInviteCodeView({
    super.key, this.onButtonTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Stack(
        children: [
          Image.asset(
            R.assetsImagesBgCashInfo,
            fit: BoxFit.cover,
            width: MediaQuery.of(context).size.width,
          ),
          buildContent(context),
        ],
      ),
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 22),
        Text(
          S.of(context).input_invite_code,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        Container(
          margin: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          height: 50,
          decoration: BoxDecoration(
            color: Color(0xFFF5F3F2),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: Colors.white, // 白色内描边
              width: 1,
            ),
          ),
          child: TextField(
            controller: textEditingController,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: S.of(context).input_invite_code,
              hintStyle: TextStyle(fontSize: 14, color: primaryTextColor),
              isDense: true,
              contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            ),
          ),
        ),
        SizedBox(height: 16),
        InkWell(
          onTap: () async {
            String inviteCode = textEditingController.text.trim();
            if(inviteCode.isEmpty) {
              makeToast(S.of(context).invite_code_empty_hint);
              return;
            }
            try {
              showLoadingDialog();
              BindInviteCodeResponse bindInviteCodeResponse = await networkApiClient.bindInviteCode(inviteCode: inviteCode);
              dismissLoadingDialog();
              if(onButtonTapped != null) {
                onButtonTapped!();
              }
              if(context.mounted) {
                Navigator.of(context).pop(context);
                showInviteGroupInformationBottomView(context, bindInviteCodeResponse: bindInviteCodeResponse);
              }
            } catch (e) {
              dismissLoadingDialog();
              if(context.mounted) {
                ApiErrorHandler.handleError(e, context);
              }
            }
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16),
            height: 44,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFFFE9C7), Color(0xFFFFE9C7)],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Center(
              child: Text(
                S.of(context).next_step,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
      ],
    );
  }
}

void showBottomInputInviteCodeView(
    BuildContext context, {
      VoidCallback? onButtonTapped,
    }) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return BottomInputInviteCodeView(onButtonTapped: onButtonTapped);
    },
  );
}