import 'package:flutter/material.dart';
import 'package:milestone/generated/assets.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/utils/user_utils.dart';

import '../generated/l10n.dart';
import '../models/bind_invite_code_response.dart';
import '../r.dart';
import 'image_widget.dart';
import 'member_level_badge_view.dart';

class InviteGroupInformationBottomView extends StatelessWidget {
  final BindInviteCodeResponse bindInviteCodeResponse;
  const InviteGroupInformationBottomView({
    super.key,
    required this.bindInviteCodeResponse,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Image.asset(
          R.assetsImagesBgCashInfo,
          fit: BoxFit.cover,
          width: MediaQuery.of(context).size.width,
        ),
        buildContent(context),
      ],
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 22),
        Text(
          "${S.of(context).congratulation_to_add_group}${bindInviteCodeResponse.nickname}${S.of(context).group}",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 15,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        ClipOval(
          child: ImageWidget(
            width: 52,
            height: 52,
            url: UserManager.getAvatar(),
            defaultImagePath: Assets.imagesIcAvatarDefault,
            loadingWidth: 52,
            loadingHeight: 52,
          ),
        ),
        SizedBox(height: 16),
        Text(
          UserManager.getNickname() ?? "",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: primaryTextColor,
          ),
        ),
        SizedBox(height: 16),
        MemberLevelBadgeView(memberLevel: UserManager.getMemberLevelValue()),
        SizedBox(height: 16),
        InkWell(
          onTap: () async {
            Navigator.of(context).pop();
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 16),
            height: 44,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFFFE9C7), Color(0xFFFFE9C7)],
              ),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Center(
              child: Text(
                S.of(context).finish,
                style: TextStyle(
                  color: primaryTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
      ],
    );
  }
}

void showInviteGroupInformationBottomView(
  BuildContext context, {
  VoidCallback? onButtonTapped,
  required BindInviteCodeResponse bindInviteCodeResponse,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
    ),
    builder: (context) {
      return InviteGroupInformationBottomView(
        bindInviteCodeResponse: bindInviteCodeResponse,
      );
    },
  );
}
