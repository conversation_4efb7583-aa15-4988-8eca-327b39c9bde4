import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';
import 'package:milestone/widget/background_scaffold.dart';

class LoadingErrorView extends StatelessWidget {
  final String? error;
  final VoidCallback onRetry;

  const LoadingErrorView({super.key, this.error, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    return BackgroundScaffold(
      title: "",
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(R.assetsImagesErrorLoading, width: 320),
          const SizedBox(height: 16),
          Text(
            S.of(context).error_title,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: primaryTextColor),
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () async {
              onRetry();
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 22),
              width: MediaQuery.of(context).size.width,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFFE6AC44), Color(0xFFFACD8A)],
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Center(
                child: Text(
                  S.of(context).loading_more_retry,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            )
          ),
        ],
      ),
    );
  }
}
