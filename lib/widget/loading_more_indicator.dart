import 'package:flutter/material.dart';
import 'package:loading_more_list/loading_more_list.dart';
import 'package:milestone/widget/empty_view.dart';
import '../../generated/l10n.dart';
import 'loading_error_view.dart';

class LoadingMoreIndicator extends StatelessWidget {
  const LoadingMoreIndicator(this.status,
      {super.key,
      required this.tryAgainFunction,
      required this.emptyLinkFunction});

  final IndicatorStatus status;
  final Function tryAgainFunction;
  final Function emptyLinkFunction;

  @override
  Widget build(BuildContext context) {
    return buildLoadingMoreIndicator(context);
  }

  Widget buildLoadingMoreIndicator(BuildContext context) {
    switch (status) {
      case IndicatorStatus.none:
        return Container(height: 0);
      case IndicatorStatus.loadingMoreBusying:
        return buildLoadingMoreContent(context);
      case IndicatorStatus.fullScreenBusying:
        return SliverFillRemaining(
          hasScrollBody: false,
          child: buildLoadingMoreContent(context),
        );
      case IndicatorStatus.fullScreenError:
        return SliverFillRemaining(
          hasScrollBody: false,
          child: buildErrorContent(context),
        );
      case IndicatorStatus.error:
        return buildErrorContent(context);
      case IndicatorStatus.noMoreLoad:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(S.of(context).loading_more_no_more,
                style: const TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ))
          ],
        );
      case IndicatorStatus.empty:
        return SliverFillRemaining(
          hasScrollBody: false,
          child: buildEmptyContent(context),
        );
      default:
        return Container(height: 0);
    }
  }

  Widget buildErrorContent(BuildContext context) {
    return LoadingErrorView(
        error: S.of(context).loading_more_error,
        onRetry: () {
          tryAgainFunction();
        });
  }

  Widget buildEmptyContent(BuildContext context) {
    return EmptyView();
  }
}

Widget buildLoadingMoreContent(BuildContext context) {
  return const Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [RefreshProgressIndicator()],
  );
}
