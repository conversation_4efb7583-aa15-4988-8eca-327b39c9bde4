// FAQButton.dart
import 'package:flutter/material.dart';
import 'package:milestone/generated/l10n.dart';
import 'package:milestone/r.dart';
import 'package:milestone/themes/colors.dart';

class QuestionButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String buttonText;
  final bool isCompact;

  const QuestionButton({
    super.key,
    required this.onPressed,
    this.buttonText = "FAQ", // 默认为"FAQ"，但可自定义
    this.isCompact = true, // 是否紧凑模式
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: _buildButtonStyle(),
      onPressed: onPressed,
      child: Container(
        margin: EdgeInsets.only(right: 12),
        decoration: _buildContainerDecoration(),
        child: Padding(
          padding: _calculatePadding(),
          child: _buildButtonContent(context),
        ),
      ),
    );
  }

  ButtonStyle _buildButtonStyle() {
    return ButtonStyle(
      padding: WidgetStateProperty.all(EdgeInsets.zero),
      backgroundColor: WidgetStateProperty.resolveWith<Color>(
        (Set<WidgetState> states) => Colors.transparent,
      ),
      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
        RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(16),
      gradient: const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFF6F2F25), Color(0xFF441C04)],
      ),
    );
  }

  EdgeInsets _calculatePadding() {
    if (isCompact) {
      return const EdgeInsets.symmetric(horizontal: 6, vertical: 2);
    }
    return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  }

  Widget _buildButtonContent(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // FAQ 图标
        _buildFAQIcon(),
        // 文本
        _buildButtonText(context),
        // 箭头
        _buildArrowIcon(),
      ],
    );
  }

  Widget _buildFAQIcon() {
    return Padding(
      padding: isCompact
          ? const EdgeInsets.only(right: 4)
          : const EdgeInsets.only(right: 8),
      child: Image.asset(R.assetsImagesIcHomeQuestion),
    );
  }

  Widget _buildButtonText(BuildContext context) {
    return Text(
      buttonText,
      style: isCompact
          ? Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            )
          : Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
    );
  }

  Widget _buildArrowIcon() {
    return Padding(
      padding: isCompact
          ? const EdgeInsets.only(left: 4)
          : const EdgeInsets.only(left: 8),
      child: Image.asset(R.assetsImagesIcMemberArrowRight, height: 10),
    );
  }
}
