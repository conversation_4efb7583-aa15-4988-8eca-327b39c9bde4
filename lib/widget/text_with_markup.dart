import 'package:flutter/widgets.dart';
import 'dart:collection';

class TextWithMarkup extends StatelessWidget {
  final String text;
  final Map<String, TextStyle> styles;
  final TextStyle? defaultStyle;

  const TextWithMarkup({
    super.key,
    required this.text,
    required this.styles,
    this.defaultStyle,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: _parseMarkup(text, styles, defaultStyle),
    );
  }

  TextSpan _parseMarkup(
      String input,
      Map<String, TextStyle> styles,
      TextStyle? baseStyle,
      ) {
    final List<InlineSpan> spans = [];
    final RegExp tagPattern = RegExp(r'<(\/?)(\w+)[^>]*>');
    final TextStyle defaultTextStyle = baseStyle ?? const TextStyle();

    int currentPosition = 0;
    final List<TextStyle> styleStack = [];
    TextStyle currentStyle = defaultTextStyle;

    for (final match in tagPattern.allMatches(input)) {
      // 添加标签前的文本
      if (match.start > currentPosition) {
        spans.add(TextSpan(
          text: input.substring(currentPosition, match.start),
          style: currentStyle,
        ));
      }

      // 处理标签
      final bool isClosing = match.group(1)!.isNotEmpty;
      final String tagName = match.group(2)!;

      if (isClosing) {
        // 闭合标签：弹出样式栈
        if (styleStack.isNotEmpty) {
          currentStyle = styleStack.removeLast();
        }
      } else {
        // 开始标签：推入样式栈
        if (styles.containsKey(tagName)) {
          styleStack.add(currentStyle);
          currentStyle = currentStyle.merge(styles[tagName]);
        }
      }

      currentPosition = match.end;
    }

    // 添加剩余文本
    if (currentPosition < input.length) {
      spans.add(TextSpan(
        text: input.substring(currentPosition),
        style: currentStyle,
      ));
    }

    return TextSpan(children: spans, style: defaultTextStyle);
  }
}