export default {
  navbar: {
    home: "Home",
    profile: "Profile",
    logout: "Logout"
  },
  common: {
    editSuccess: "Updated successfully",
    addSuccess: "Added successfully",
    confirmDelete: "Are you sure to delete the item named '{name}'?",
    status: "Status",
    fetchDataFailed: "Failed to fetch data",
    operationSuccess: "Operation succeeded",
    operationFailed: "Operation failed",
    confirm: "Confirm",
    cancel: "Cancel",
    deleteConfirm: "Are you sure to delete?",
    deleteSuccess: "Deleted successfully",
    deleteFailed: "Delete failed",
    saveSuccess: "Saved successfully",
    saveFailed: "Save failed",
    enterRejectReason: "Please enter reject reason",
    startDate: "Start Date",
    endDate: "End Date",
    all: "All",
    serialNumber: "Serial Number",
    query: "Query",
    reset: "Reset",
    enter: "Please enter",
    pendingReview: "Pending Review",
    reviewedPassed: "Reviewed Passed",
    reviewedRejected: "Reviewed Rejected",
    pleaseSelect: "Please Select",
    yes: "Yes",
    no: "No",
    show: "Show",
    hide: "Hide",
    keyword: {
      text: "Text Message",
      image: "Image Message",
      news: "News Message",
      voice: "Voice Message"
    },
    couponType: {
      general: "General Coupon",
      product: "Product Coupon",
      category: "Category Coupon"
    },
    couponReceive: {
      manual: "Manual Claim",
      newUser: "New User Coupon",
      gift: "Gift Coupon"
    },
    paymentStatus: {
      unpaid: "Unpaid",
      paid: "Paid"
    },
    withdrawType: {
      bank: "Bank Card",
      alipay: "Alipay",
      wechat: "WeChat"
    },
    rechargeType: {
      wechatPublic: "WeChat Official Account",
      wechatH5: "WeChat H5 Payment",
      miniProgram: "Mini Program"
    },
    withdrawStatus: {
      rejected: "Rejected",
      reviewing: "Under Review",
      withdrawn: "Withdrawn"
    },
    status: {
      bargain: {
        1: "In Progress",
        2: "Unfinished",
        3: "Completed"
      }
    },
    onePass: {
      sms: "SMS",
      copy: "Product Collection",
      expr_query: "Logistics Inquiry",
      expr_dump: "E-waybill Printing"
    },
    editStatus: {
      1: "Unreviewed",
      2: "Under Review",
      3: "Review Failed",
      4: "Reviewed Successfully"
    },
    videoStatus: {
      0: "Initial Value",
      5: "Published",
      11: "Manually Unpublished",
      13: "Violation Removed / Risk Control Removed"
    }
  },
  appMain: {
    copyright: "Copyright © 2025"
  },
  dashboard: {
    home: "Home",
    brandCenter: "Brand Center",
    brandManage: "Brand Management",
    productManage: "Product Management",
    appManage: "App Management",
    homeManage: "Homepage Management",
    opsCenter: "Operations Center",
    withdrawalReview: "Withdrawal Review",
    withdrawalRecords: "Withdrawal Records",
    orderCenter: "Order Center",
    orderInquiry: "Order Inquiry",
    userCenter: "User Center",
    userManage: "User Management",
    financeCenter: "Finance Center",
    financeDetails: "Financial Details",
    withdrawalRequest: "Withdrawal Request",
    paramSettings: "Parameter Settings",
    rewardRules: "Reward Rules Setup",
    withdrawalFee: "Withdrawal Fee Setup",
    membershipFee: "Membership Upgrade Fee Setup",
    accountCenter: "Account Center",
    adminPermissions: "Admin Permissions",
    roleManage: "Role Management",
    adminList: "Admin List",
    permissionRules: "Permission Rules",
    profile: "Profile",
    systemSettings: "System settings",
    chainTransferRecord: "Chain transfer record",
    platformCashbackRate: "Platform Cashback Rate Settings",
    shoppingCashbackRules: "Shopping Cashback Rules"
  },
  platformCashbackRate: {
    platformCashbackRate: "Tingkat pengembalian platform",
    editTitle: "Edit Tingkat Pengembalian Platform",
    addTitle: "Tambah Tingkat Pengembalian Platform",
    placeholder: {
      platformCashbackRate: "Masukkan tingkat pengembalian platform"
    }
  },
  tagsView: {
    refresh: "Refresh",
    close: "Close",
    closeOthers: "Close Others",
    closeAll: "Close All"
  },
  homepage: {
    welcome: "Welcome to the GENCO Admin Panel!",
    paymentSwitch: "Payment Switch",
    paymentSwitchTip1:
      "When enabled, the frontend allows users to become agents and partners.",
    paymentSwitchTip2:
      "When disabled, upgrades are not allowed. This is only for AppStore submission.",
    paymentSwitchTip3: "Do not operate casually.",
    loginMode: "Login Method",
    loginModeTip1:
      "This setting only controls the display of TikTok and SMS login options",
    loginModeTip2:
      "on the login page for AppStore submission. Do not operate casually.",
    tikTokLogin: "TikTok Login",
    smsLogin: "SMS Login",
    submit: "Submit"
  },
  brand: {
    search: "Brand Search:",
    status: "Status:",
    pleaseSelect: "Please Select",
    reset: "Reset",
    query: "Query",
    addBrand: "Add Brand",
    batchOnline: "Batch Online",
    batchOffline: "Batch Offline",
    batchDelete: "Batch Delete",
    brandLogo: "Brand Logo",
    brandName: "Brand Name",
    industry: "Industry",
    platform: "Platform",
    productCount: "Product Count",
    maxCashback: "Max Cashback Rate",
    soldCount: "Sold Count",
    soldAmount: "Sold Amount (Rp)",
    cashbackAmount: "Cashback Amount (Rp)",
    shareCount: "Share Count",
    createTime: "Create Time",
    creator: "Creator",
    statusLabel: "Status",
    isHot: "Is Hot Brand",
    isHighCashback: "Is High Cashback Brand",
    offline: "Offline",
    online: "Online",
    edit: "Edit",
    delete: "Delete",
    addDialogTitle: "Add Brand",
    brandNameInput: "Enter brand name",
    brandLogoInput: "Enter image URL",
    contactPerson: "Contact Person:",
    contactPhone: "Contact Phone:",
    isOnline: "Is Online:",
    confirm: "Confirm",
    cancel: "Cancel",
    platformTiktok: "TikTok",
    platformShopee: "Shopee",
    confirmOperation: "Are you sure to perform this operation?",
    prompt: "Prompt",
    productList: "Product",
    isOnline: "On Sale",
    isOutline: "Pending",
    isOuted: "Offline",
    selectTip: "Please select"
  },
  product: {
    search: "Product Search:",
    keywordsPlaceholder: "Enter product name or keywords",
    status: "Status:",
    pleaseSelect: "Please Select",
    query: "Query",
    reset: "Reset",
    addProduct: "Add Product",
    batchOnline: "Batch Online",
    batchOffline: "Batch Offline",
    batchDelete: "Batch Delete",
    productImage: "Product Image",
    productName: "Product Name",
    productPrice: "Product Price",
    cashbackRate: "Cashback Rate",
    estimatedCashback: "Estimated Cashback (Rp)",
    productLink: "Product Link",
    shareCount: "Share Count",
    soldCount: "Sold Count",
    cashbackAmount: "Cashback Amount (Rp)",
    addTime: "Add Time",
    action: "Action",
    offline: "Offline",
    online: "Online",
    edit: "Edit",
    delete: "Delete",
    isHot: "Hot",
    isBenefit: "High Cashback",
    isTikTok: "TikTok",
    addDialogTitle: "Add Product",
    enterProductLink: "Enter product link",
    fetchProductInfo: "Fetch Product Info",
    enterProductName: "Enter product name",
    productPrice: "Product Price",
    enterProductPrice: "Enter product price",
    // cashbackRate: "Cashback Rate",
    enterCashbackRate: "Enter cashback rate",
    // estimatedCashback: "Estimated Cashback",
    enterCashbackAmount: "Enter cashback amount",
    isOnline: "Is Online:",
    yes: "Yes",
    no: "No",
    confirm: "Confirm",
    cancel: "Cancel",
    all: "All",
    fetchProductFailed: "Failed to fetch product info",
    isOnIndex: "Display on the homepage?",
    usercashbackRate: "User Cashback Rate",
    isOnline: "On Sale",
    isOutline: "Pending",
    isOuted: "Offline"
  },
  operations: {
    withdrawal: {
      walletWithdrawal: "Wallet Withdrawal",
      bankWithdrawal: "Bank Withdrawal",
      applicant: "Applicant",
      applicationTime: "Application Time",
      electronicWallet: "Electronic Wallet",
      bankName: "Bank Name",
      applicationId: "Application ID",
      applicantName: "Applicant",
      withdrawalAmount: "Withdrawal Amount",
      serviceFee: "Service Fee",
      actualAmount: "Actual Amount",
      walletCode: "Wallet Code",
      walletAccount: "Account",
      bankCardNumber: "Bank Card Number",
      name: "Name",
      phoneNumber: "Phone Number",
      withdrawalCount: "Withdrawal Count",
      auditResult: "Audit Result",
      rejectReason: "Reject Reason",
      approve: "Approve",
      reject: "Reject",
      rejectReview: "Reject Review",
      exportExcel: "Export Excel",
      transferTime: "Transfer Time",
      transferResult: "Transfer Result",
      remark: "Remark",
      attachment: "Attachment",
      operator: "Operator",
      withdrawalStatus: "Withdrawal Status",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay",
      unapproved: "Unapproved",
      underReview: "Under Review",
      reviewed: "Reviewed",
      paid: "Paid"
    }
  },
  order: {
    search: {
      orderNo: "Order Number",
      productTitle: "Product Name",
      status: "Status",
      all: "All",
      query: "Query",
      reset: "Reset",
      exportExcel: "Export Excel",
      serialNumber: "Serial Number",
      productImage: "Product Image",
      orderId: "Order ID",
      productName: "Product Name",
      payCount: "Purchase Count",
      actualCommission: "Product Price (Rp)",
      payPrice: "Order Amount (Rp)",
      commissionRate: "Product Cashback Rate",
      estimatedCommission: "Estimated Cashback (Rp)",
      contentId: "E-commerce Platform",
      statusLabel: "Order Status",
      unknown: "Unknown",
      ordered: "Ordered",
      settled: "Settled",
      refunded: "Refunded",
      frozen: "Frozen",
      deducted: "Deducted",
      totalPrice: "Amount",
      userCashBackRate: "User Cashback Rate",
      creatTime: "Order Time"
    }
  },
  user: {
    center: {
      nickname: "Nickname",
      phone: "Phone Number",
      userLevel: "User Level",
      query: "Query",
      reset: "Reset",
      serialNumber: "Serial Number",
      avatar: "Avatar",
      tiktokAccount: "TikTok Account",
      tiktokId: "TikTok ID",
      whatsApp: "WhatsApp",
      registerTime: "Registration Time",
      lastLoginTime: "Last Login Time",
      orderCount: "Order Count",
      orderFinishCount: "Order Finish Count",
      isAgent: "Is Agent",
      isPartner: "Is Partner",
      userLevelLabel: "User Level",
      inviter: "Inviter",
      userTags: "User Tags"
    }
  },
  financial: {
    detail: {
      title: "Financial Details",
      purchaseDetail: "Membership Purchase Details",
      tradeDetail: "Transaction Details",
      rechargeType: "Product Name",
      transactionTime: "Transaction Time",
      paymentMethod: "Payment Method",
      electronicWallet: "Electronic Wallet",
      bankName: "Payment Bank",
      serialNumber: "Serial Number",
      paymentTime: "Payment Time",
      paymentNo: "Payment No.",
      actualPaymentAmount: "Actual Payment Amount",
      institutionNumber: "Institution No.",
      paymentAccount: "Payment Account",
      mobile: "Phone Number",
      payee: "Payee",
      payeeAccount: "Payee Account",
      tradeNo: "Transaction No.",
      tradeType: "Transaction Type",
      tradeAmount: "Transaction Amount (Rp)",
      userNickname: "User Nickname",
      tikTokAccount: "TikTok NickName",
      whatsApp: "WhatsApp",
      channel: "Channel",
      orderNo: "Order No.",
      bankTransfer: "Bank Transfer",
      electronicWallet: "Electronic Wallet",
      agentFee: "Agent Fee",
      partnerFee: "Partner Fee",
      exportExcel: "Export Excel",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay"
    },
    request: {
      walletWithdrawal: "Wallet Withdrawal",
      bankWithdrawal: "Bank Withdrawal",
      applicant: "Applicant",
      applicationTime: "Application Time",
      electronicWallet: "Electronic Wallet",
      bankName: "Bank Name",
      serialNumber: "Serial Number",
      applicationId: "Application ID",
      applicantName: "Applicant",
      withdrawalAmount: "Withdrawal Amount",
      serviceFee: "Service Fee",
      actualAmount: "Actual Amount",
      applicationTime: "Application Time",
      walletCode: "Electronic Wallet",
      walletAccount: "Account",
      bankCardNumber: "Bank Card Number",
      name: "Name",
      phoneNumber: "Phone Number",
      action: "Action",
      transferComplete: "Transfer Complete",
      attachment: "Attachment",
      remark: "Remark",
      confirm: "Confirm",
      cancel: "Cancel",
      exportExcel: "Export Excel"
    },
    history: {
      walletWithdrawal: "Wallet Withdrawal",
      bankWithdrawal: "Bank Withdrawal",
      applicant: "Applicant",
      applicationTime: "Application Time",
      electronicWallet: "Electronic Wallet",
      bankName: "Bank Name",
      serialNumber: "Serial Number",
      applicationId: "Application ID",
      applicantName: "Applicant",
      withdrawalAmount: "Withdrawal Amount",
      serviceFee: "Service Fee",
      actualAmount: "Actual Amount",
      applicationTime: "Application Time",
      walletCode: "Electronic Wallet",
      walletAccount: "Account",
      bankCardNumber: "Bank Card Number",
      name: "Name",
      phoneNumber: "Phone Number",
      transferTime: "Transfer Time",
      transferResult: "Transfer Result",
      remark: "Remark",
      attachment: "Attachment",
      operator: "Operator",
      exportExcel: "Export Excel",
      status: "Withdrawal Status"
    }
  },
  parameter: {
    rewardRules: {
      title: "Reward Rules Setup",
      rewardTemplateName: "Template Name",
      rewardTemplateId: "Reward Rule Template ID",
      directInviteReward: "Direct Invite Reward per Person",
      secondLevelInviteReward: "Second Level Invite Reward per Person (Rp)",
      thirdLevelInviteReward: "Third Level Invite Reward per Person (Rp)",
      goldRewardPer10: "Gold Reward per 10 Invites (Rp)",
      diamondRewardPer10: "Diamond Reward per 10 Invites (Rp)",
      operation: "Operation",
      edit: "Edit",
      editTitle: "Edit Reward Rule Settings",
      directAgentLabel: "Direct invitee is agent",
      directPartnerLabel: "Direct invitee is partner",
      indirectAgent2LevelLabel:
        "Indirect invite (Level 2 is agent) reward per person (Rp)",
      indirectPartner2LevelLabel:
        "Indirect invite (Level 2 is partner) reward per person (Rp)",
      indirectAgent3LevelLabel:
        "Indirect invite (Level 3 is agent) reward per person (Rp)",
      indirectPartner3LevelLabel:
        "Indirect invite (Level 3 is partner) reward per person (Rp)"
    },
    withdrawalFee: {
      title: "Withdrawal Fee Setup",
      feeTemplateId: "Fee Template ID",
      minWithdrawAmount: "Minimum Withdrawal Amount (Rp)",
      maxWithdrawAmount: "Maximum Withdrawal Amount (Rp)",
      withdrawFeeRate: "Withdrawal Fee Rate (%)",
      operation: "Operation",
      edit: "Edit",
      addTitle: "Add Fee Rule",
      editTitle: "Edit Fee Rule",
      placeholder: {
        couponId: "Please enter coupon ID",
        minWithdrawAmount: "Please enter minimum withdrawal amount",
        maxWithdrawAmount: "Please enter maximum withdrawal amount",
        withdrawFeeRate: "Please enter withdrawal fee rate"
      }
    },
    membershipFee: {
      title: "Membership Upgrade Fee Setup",
      feeTemplateId: "Fee Template ID",
      agentFee: "Agent Fee (Rp)",
      partnerFee: "Partner Fee (Rp)",
      operation: "Operation",
      edit: "Edit",
      addTitle: "Add Membership Upgrade Fee",
      editTitle: "Edit Membership Upgrade Fee",
      placeholder: {
        agentFee: "Please enter agent fee",
        partnerFee: "Please enter partner fee"
      }
    },
    shoppingCashbackRules: {
      directCashbackRate: "Direct Cashback Rate (%)",
      secondLevelCashbackRate: "Second Level Cashback Rate (%)",
      thirdLevelCashbackRate: "Third Level Cashback Rate (%)",
      normalUserRule: "Normal User Cashback Rule",
      agentTeamRule: "Agent Team Cashback Rule",
      partnerTeamRule: "Partner Team Cashback Rule"
    }
  },
  admin: {
    system: {
      role: {
        roleName: "Role Name",
        roleId: "Role ID",
        status: "Status",
        createTime: "Create Time",
        updateTime: "Update Time",
        operation: "Operation",
        addRole: "Add Role",
        editRole: "Edit Role",
        deleteRole: "Delete Role",
        confirmDelete: "Are you sure to delete current data?",
        deleteSuccess: "Role deleted successfully",
        createIdentity: "Create Identity",
        editIdentity: "Edit Identity",
        roleForm: {
          roleNameLabel: "Role Name",
          roleNamePlaceholder: "Identity Name",
          statusLabel: "Status",
          menuPermissions: "Menu Permissions",
          expandCollapse: "Expand/Collapse",
          selectAll: "Select All/None",
          parentChildLink: "Parent-Child Link",
          confirm: "Confirm",
          update: "Update",
          cancel: "Cancel"
        }
      },
      admin: {
        role: "Role",
        status: "Status",
        realName: "Name or Account",
        id: "ID",
        account: "Account",
        phone: "Phone",
        lastTime: "Last Login Time",
        lastIp: "Last Login IP",
        isSms: "Receive SMS",
        isDel: "Delete Flag",
        operation: "Operation",
        addAdmin: "Add Admin",
        edit: "Edit",
        delete: "Delete",
        createIdentity: "Create Identity",
        editIdentity: "Edit Identity",
        pleaseAddPhone: "Please add a phone number for the admin first!",
        confirmDelete: "Are you sure to delete current data?",
        deleteSuccess: "Data deleted successfully",
        account: "Account",
        pwd: "Password",
        repwd: "Confirm Password",
        realName: "Real Name",
        roles: "Roles",
        phone: "Phone",
        pleaseAddPhone: "Please add a phone number for the admin first!",
        validatePhone: {
          required: "Please enter phone number",
          formatError: "Phone number format is incorrect!"
        },
        validatePass: {
          required: "Please re-enter password",
          notMatch: "Passwords do not match!"
        },
        message: {
          createSuccess: "Admin created successfully",
          updateSuccess: "Admin updated successfully"
        },
        validateAccount: {
          required: "Please enter admin account"
        },
        validatePassword: {
          required: "Please enter admin password"
        },
        validateConfirmPassword: {
          required: "Please confirm password"
        },
        validateRealName: {
          required: "Please enter admin name"
        },
        validateRoles: {
          required: "Please select admin role"
        },
        validatePassword: {
          required: "Please enter admin password",
          lengthError: "Password length should be 6-20 characters"
        },
        validateConfirmPassword: {
          required: "Please confirm password"
        },
        validatePass: {
          notMatch: "Passwords do not match"
        }
      }
    }
  },
  permissionRules: {
    menuName: "Menu Name",
    status: "Status",
    select: "Please Select",
    add: "Add",
    expandCollapse: "Expand/Collapse",
    actions: {
      edit: "Edit",
      add: "Add",
      delete: "Delete"
    },
    table: {
      menuName: "Menu Name",
      icon: "Icon",
      sort: "Sort",
      perm: "Permission",
      component: "Component Path",
      status: "Status",
      createTime: "Create Time",
      type: "Type"
    },
    menuType: {
      directory: "Directory",
      menu: "Menu",
      button: "Button"
    },
    form: {
      parentMenu: "Parent Menu",
      menuType: "Menu Type",
      menuIcon: "Menu Icon",
      menuName: "Menu Name",
      sort: "Display Sort",
      component: "Component Path",
      componentTip:
        "Component path, e.g. `system/user/index`, default in `views` folder",
      perm: "Permission Character",
      permTip:
        'Permission character defined in controller, e.g. @PreAuthorize(`@ss.hasPermi("system: user: list")`)',
      showStatus: "Display Status",
      showStatusTip:
        "If hidden, the route will not appear in the sidebar but can still be accessed",
      enterMenuName: "Please enter menu name",
      enterComponent: "Please enter component path",
      enterPerm: "Please enter permission character",
      selectIcon: "Please select menu icon",
      selectParentMenu: "Select parent menu",
      sortRequired: "Display sort cannot be empty"
    }
  },
  chainTransferRecord: {
    title: "Chain Transfer Record",
    keyword: "Keyword",
    brandName: "Brand Name",
    query: "Query",
    reset: "Reset",
    exportExcel: "Export Excel",
    serialNumber: "Serial Number",
    nickname: "Nickname",
    tiktokId: "TikTok ID",
    originalLink: "Original Link",
    rebateLink: "Rebated Link After Chain Transfer",
    operationTime: "Operation Time",
    linkSource: "Link Source",
    productId: "Product ID",
    productName: "Product Name",
    productPrice: "Product Price",
    productCashbackRate: "Product Cashback Rate",
    userCashbackRate: "User Cashback Rate"
  },
  message: {
    hello: "Hello",
    userNotice: "User Notice",
    userDetails: {
      balance: "Balance",
      allOrderCount: "Total Orders",
      allConsumeCount: "Total Consumption",
      integralCount: "Points",
      mothOrderCount: "Orders This Month",
      mothConsumeCount: "Consumption This Month",
      consumeRecord: "Consumption Record",
      integralDetail: "Points Detail",
      signInRecord: "Sign-in Record",
      coupons: "Owned Coupons",
      balanceChange: "Balance Change",
      friendRelation: "Friend Relation",
      sourceOrPurpose: "Source/Purpose",
      integralChange: "Points Change",
      balanceAfterChange: "Points After Change",
      date: "Date",
      remark: "Remark",
      orderId: "Order ID",
      receiver: "Receiver",
      goodsNum: "Goods Quantity",
      goodsTotalPrice: "Goods Total Price",
      payPrice: "Paid Amount",
      payTime: "Transaction Time",
      action: "Action",
      getIntegral: "Points Earned",
      signTime: "Sign-in Time",
      couponName: "Coupon Name",
      faceValue: "Face Value",
      validity: "Validity",
      minPrice: "Min Consumption",
      exchangeTime: "Exchange Time",
      changeAmount: "Change Amount",
      afterChange: "After Change",
      type: "Type",
      createTime: "Create Time",
      id: "ID",
      nickname: "Nickname",
      level: "Level",
      joinTime: "Join Time"
    }
  }
};
