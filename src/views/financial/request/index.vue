<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="searchForm.extractType"
        @tab-click="onChangeType"
        class="mb20"
      >
        <el-tab-pane
          :label="$t('financial.request.walletWithdrawal')"
          name="wallet"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('financial.request.bankWithdrawal')"
          name="bank"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('financial.request.applicant') + '：'">
            <el-input
              v-model="searchForm.keywords"
              size="small"
              :placeholder="$t('common.enter')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('financial.request.applicationTime') + '：'">
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item
            :label="$t('financial.request.electronicWallet') + '：'"
            v-if="searchForm.extractType == 'wallet'"
          >
            <el-select
              v-model="searchForm.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="$t('operations.withdrawal.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('financial.request.bankName') + '：'"
            v-if="searchForm.extractType == 'bank'"
          >
            <el-select
              v-model="searchForm.bankName"
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList(1)">{{
        $t("common.query")
      }}</el-button>

      <el-button size="small" type="" class="mr10" @click="resetForm">{{
        $t("common.reset")
      }}</el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <el-button type="primary" size="small" @click="handleUpload">{{
          $t("financial.request.exportExcel")
        }}</el-button>
      </div>
      <div>
        <el-table
          v-loading="loading"
          :data="tableData"
          size="small"
          :header-cell-style="{ fontWeight: 'bold' }"
        >
          <el-table-column
            type="index"
            :label="$t('common.serialNumber')"
            min-width="110"
          >
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.applicationId')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.id | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.applicantName')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.realName | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.withdrawalAmount')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.extractPrice | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.serviceFee')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.serviceFee | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.actualAmount')"
            min-width="100"
          >
            <template slot-scope="scope">{{
              scope.row.actualAmount | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.applicationTime')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.createTime | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            v-if="searchForm.extractType === 'wallet'"
            :label="$t('financial.request.electronicWallet')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.walletCode | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            v-if="searchForm.extractType == 'wallet'"
            :label="$t('financial.request.walletAccount')"
            min-width="80"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.walletAccount | filterEmpty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="searchForm.extractType == 'bank'"
            :label="$t('financial.request.bankName')"
            min-width="80"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.bankName | filterEmpty }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="searchForm.extractType == 'bank'"
            :label="$t('financial.request.bankCardNumber')"
            min-width="80"
          >
          </el-table-column>
          <el-table-column :label="$t('financial.request.name')" min-width="80">
            <template slot-scope="scope">{{
              scope.row.nickName | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.phoneNumber')"
            min-width="80"
          >
            <template slot-scope="scope">{{
              scope.row.phone | filterEmpty
            }}</template>
          </el-table-column>
          <el-table-column
            :label="$t('financial.request.action')"
            min-width="80"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="small"
                type="text"
                @click="handleFinish(scope.row)"
                >{{ $t("financial.request.transferComplete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="mt20"
          @size-change="e => sizeChange"
          @current-change="e => pageChange"
          :current-page="searchForm.page"
          :page-sizes="[20, 40, 60, 100]"
          :page-size="searchForm.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="searchForm.total"
        >
        </el-pagination>
      </div>

      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="$t('financial.request.transferComplete')"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="artFrom"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('financial.request.attachment') + '：'"
            prop="voucherImage"
          >
            <el-upload
              class="avatar-uploader"
              action
              :show-file-list="false"
              :http-request="handleUploadForm"
              :on-change="imgSaveToUrl"
              :before-upload="beforeAvatarUpload"
              :headers="myHeaders"
              multiple
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-image
              v-if="artFrom.voucherImage"
              style="width: 36px; height: 36px;margin-top: 8px;"
              :src="artFrom.voucherImage"
              :preview-src-list="[artFrom.voucherImage]"
            />
          </el-form-item>
          <el-form-item
            :label="$t('financial.request.remark') + '：'"
            prop="remark"
          >
            <el-input
              v-model="artFrom.remark"
              size="small"
              :placeholder="$t('financial.request.remark')"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="handleCancle">
            {{ $t("common.cancel") }}
          </el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  applyListApi,
  extractBankApi,
  financeApplyDealApi,
  uploadImage
} from "@/api/financial";
import { getToken } from "@/utils/auth";

export default {
  name: "WithdrawalRequest",
  components: {},
  data() {
    return {
      loading: false,
      tableData: [],
      realName: "",
      pid: "",
      myHeaders: { "X-Token": getToken() },
      isMore: "",
      modelName: "",
      searchForm: {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: "wallet",
        page: 1,
        limit: 20,
        total: 0
      },
      timeList: [],
      dialogFormVisible: false,
      artFrom: {
        id: null,
        voucherImage: "",
        remark: ""
      },
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      bankList: [],

      rules: {
        voucherImage: [
          {
            required: true,
            message: "请选择",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
    this.getBankList();
  },
  methods: {
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.searchForm.page = num ? num : this.searchForm.page;
      this.searchForm.dateLimit = this.timeList.length
        ? this.timeList.join(",")
        : "";
      applyListApi(this.searchForm)
        .then(res => {
          this.tableData = res.list;
          this.searchForm.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetForm() {
      this.searchForm = {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: this.searchForm.extractType,
        page: 1,
        limit: 20,
        total: 0
      };
      this.timeList = [];
      this.getList();
    },
    //切换页数
    pageChange(index) {
      this.searchForm.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.searchForm.limit = index;
      this.getList();
    },
    handleUpload() {},
    onChangeType(tab) {
      this.getList();
    },
    handelConfirm() {
      this.$refs.elForm.validate(async valid => {
        if (!valid) return;
        financeApplyDealApi(this.artFrom).then(res => {
          this.$message.success("操作成功");
          this.handleCancle();
          getList(1);
        });
      });
    },
    handleFinish(row) {
      this.dialogFormVisible = true;
      this.artFrom.id = row.id;
      this.pid = row.id;
      this.realName = row.realName;
    },
    handleCancle() {
      this.dialogFormVisible = false;
      this.artFrom = {
        id: null,
        voucherImage: "",
        remark: ""
      };
    },

    // 选取图片后自动回调，里面可以获取到文件
    imgSaveToUrl(event) {
      // 也可以用file
      this.localFile = event.raw; // 或者 this.localFile=file.raw

      // 转换操作可以不放到这个函数里面，
      // 因为这个函数会被多次触发，上传时触发，上传成功也触发
      let reader = new FileReader();
      reader.readAsDataURL(this.localFile); // 这里也可以直接写参数event.raw

      // 转换成功后的操作，reader.result即为转换后的DataURL ，
      // 它不需要自己定义，你可以console.integralLog(reader.result)看一下
      reader.onload = () => {
        // console.integralLog(reader.result)
      };

      /* 另外一种本地预览方法 */
      let URL = window.URL || window.webkitURL;
      this.localImg = URL.createObjectURL(event.raw);
      // 转换后的地址为 blob:http://xxx/7bf54338-74bb-47b9-9a7f-7a7093c716b5
    },

    beforeAvatarUpload(rawFile) {
      if (rawFile.type === "image/jpeg" || rawFile.type === "image/png") {
        return true;
      } else {
        this.$message.error("Avatar picture must be JPG format!");
        return false;
      }
    },
    // 上传
    handleUploadForm(param) {
      const formData = new FormData();
      const data = {
        model: this.realName,
        pid: this.pid
      };
      formData.append("multipart", param.file);
      let loading = this.$loading({
        lock: true,
        text: "上传中，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      uploadImage(formData, data)
        .then(res => {
          loading.close();
          this.$message.success("上传成功");
          this.artFrom.voucherImage = res.url;
        })
        .catch(res => {
          loading.close();
        });
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>

<style scoped>
.avatar-uploader {
  width: 120px;
  height: 120px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #ccc;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  line-height: 120px;
}
</style>
