<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          :label="$t('platformCashbackRate.platformCashbackRate') + '(%)'"
          min-width="80"
          prop="platform_cash_back_rate"
        >
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('parameter.withdrawalFee.operation')"
          width="80"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              >{{ $t("parameter.withdrawalFee.edit") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="dialogTitle"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="form"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('parameter.rewardRules.rewardTemplateId')"
            prop="id"
          >
            <el-input v-model="form.id" size="small" disabled />
          </el-form-item>
          <el-form-item
            :label="$t('platformCashbackRate.platformCashbackRate') + '(%)：'"
            prop="platform_cash_back_rate"
          >
            <el-input
              v-model="form.platform_cash_back_rate"
              size="small"
              :placeholder="$t('parameter.withdrawalFee.placeholder.couponId')"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="handleCancle">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "PlatformCashbackRate",
  data() {
    return {
      loading: false,
      searchFrom: {
        formId: 113
      },
      tableData: [],
      dialogTitle: this.$t(
        "platformCashbackRate.addTitle"
      ),
      dialogFormVisible: false,
      form: {
        id: "",
        platform_cash_back_rate: ""
      },
      rules: {
        platform_cash_back_rate: [
          {
            required: true,
            message: this.$t(
              "platformCashbackRate.placeholder.platformCashbackRate"
            ),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList(num) {
      this.loading = true;

     configInfo(this.searchFrom)
        .then(res => {
          if (res) {
            this.tableData = [res];
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleEdit(row) {
      this.dialogTitle = this.$t(
        "platformCashbackRate.editTitle"
      );
      this.dialogFormVisible = true;
      this.form.id = row.id;
      this.form.platform_cash_back_rate = row.platform_cash_back_rate;
    },
    handleCancle() {
      (this.form = {
        id: "",
        platform_cash_back_rate: ""
      }),
        (this.dialogFormVisible = false);
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        const param = {
          id: this.form.id,
          sort: 1,
          status: true,
          fields: [
            {
              name: "platform_cash_back_rate",
              value: this.form.platform_cash_back_rate,
              title: "platform_cash_back_rate"
            }
          ]
        };
        configSaveForm(param).then(res => {
          this.$message.success(this.$t("common.operationSuccess"));

          this.handleCancle();
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>
