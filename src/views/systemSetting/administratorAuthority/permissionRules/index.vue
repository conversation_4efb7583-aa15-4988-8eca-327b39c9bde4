<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item :label="$t('permissionRules.menuName')" prop="menuName">
          <el-input
            v-model="queryParams.name"
            :placeholder="$t('permissionRules.form.enterMenuName')"
            clearable
            size="small"
          />
        </el-form-item>
        <el-form-item :label="$t('permissionRules.status')" prop="menuType">
          <el-select
            v-model="queryParams.menuType"
            :placeholder="$t('permissionRules.select')"
            clearable
            size="small"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >{{ $t("common.query") }}</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
            $t("common.reset")
          }}</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >{{ $t("permissionRules.actions.add") }}</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="el-icon-sort"
            size="mini"
            @click="toggleExpandAll"
            >{{ $t("permissionRules.expandCollapse") }}</el-button
          >
        </el-col>
      </el-row>

      <el-table
        v-if="refreshTable"
        v-loading="listLoading"
        :data="menuList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          :label="$t('permissionRules.table.menuName')"
          :show-overflow-tooltip="true"
          width="160"
        >
          <template slot-scope="scope">
            {{
              $t("dashboard." + scope.row.name)
                ? $t("dashboard." + scope.row.name)
                : scope.row.name
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="icon"
          :label="$t('permissionRules.table.icon')"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <i :class="'el-icon-' + scope.row.icon" style="font-size: 20px;" />
          </template>
        </el-table-column>
        <el-table-column
          prop="sort"
          :label="$t('permissionRules.table.sort')"
          width="60"
        ></el-table-column>
        <el-table-column
          prop="perms"
          :label="$t('permissionRules.table.perm')"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="component"
          :label="$t('permissionRules.table.component')"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="isShow"
          :label="$t('permissionRules.table.status')"
          width="80"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.isShow ? '' : 'danger'">{{
              scope.row.isShow ? $t("common.show") : $t("common.hide")
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('permissionRules.table.type')" width="80">
          <template slot-scope="scope">
            <span class="type_tag one" v-if="scope.row.menuType == 'M'">{{
              $t("permissionRules.menuType.directory")
            }}</span>
            <span class="type_tag two" v-else-if="scope.row.menuType == 'C'">{{
              $t("permissionRules.menuType.menu")
            }}</span>
            <span class="type_tag three" v-else type="info">{{
              $t("permissionRules.menuType.button")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('common.actions')"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['admin:system:menu:info']"
              >{{ $t("permissionRules.actions.edit") }}</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAdd(scope.row)"
              v-hasPermi="['admin:system:menu:add']"
              >{{ $t("permissionRules.actions.add") }}</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['admin:system:menu:delete']"
              >{{ $t("permissionRules.actions.delete") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加或修改菜单对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="680px"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="24">
              <el-form-item :label="$t('permissionRules.form.parentMenu')">
                <treeselect
                  v-model="form.pid"
                  :options="menuOptions"
                  :normalizer="normalizer"
                  :show-count="true"
                  :placeholder="$t('permissionRules.form.selectParentMenu')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                :label="$t('permissionRules.form.menuType')"
                prop="menuType"
              >
                <el-radio-group v-model="form.menuType">
                  <el-radio label="M">{{
                    $t("permissionRules.menuType.directory")
                  }}</el-radio>
                  <el-radio label="C">{{
                    $t("permissionRules.menuType.menu")
                  }}</el-radio>
                  <el-radio label="A">{{
                    $t("permissionRules.menuType.button")
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                v-if="form.menuType != 'A'"
                :label="$t('permissionRules.form.menuIcon')"
              >
                <el-form-item>
                  <el-input
                    :placeholder="$t('permissionRules.form.selectIcon')"
                    v-model="form.icon"
                  >
                    <el-button
                      slot="append"
                      icon="el-icon-circle-plus-outline"
                      @click="addIcon"
                    ></el-button>
                  </el-input>
                </el-form-item>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$t('permissionRules.form.menuName')"
                prop="menuName"
              >
                <el-input
                  v-model="form.name"
                  :placeholder="$t('permissionRules.form.enterMenuName')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$t('permissionRules.form.sort')"
                prop="sort"
              >
                <el-input-number
                  v-model="form.sort"
                  controls-position="right"
                  :min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.menuType !== 'A'">
              <el-form-item prop="component">
                <span slot="label">
                  <el-tooltip
                    :content="$t('permissionRules.form.componentTip')"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  {{ $t("permissionRules.form.component") }}
                </span>
                <el-input
                  v-model="form.component"
                  :placeholder="$t('permissionRules.form.enterComponent')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="form.menuType != 'M'">
                <el-input
                  v-model="form.perms"
                  :placeholder="$t('permissionRules.form.enterPerm')"
                  maxlength="100"
                />
                <span slot="label">
                  <el-tooltip
                    :content="$t('permissionRules.form.permTip')"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  {{ $t("permissionRules.form.perm") }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <span slot="label">
                  <el-tooltip
                    :content="$t('permissionRules.form.showStatusTip')"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  {{ $t("permissionRules.form.showStatus") }}
                </span>
                <el-radio-group v-model="form.isShow">
                  <el-radio
                    v-for="item in showStatus"
                    :key="item.value"
                    :label="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            v-hasPermi="['admin:system:menu:update']"
            >{{ $t("common.confirm") }}</el-button
          >
          <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  menuListApi,
  menuInfo,
  menuUpdate,
  menuAdd,
  menuDelete
} from "@/api/systemadmin";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Debounce } from "@/utils/validate";

export default {
  name: "Menu",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      listLoading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        name: "",
        menuType: ""
      },
      // 表单参数
      form: {},
      // 请求到的menu数据
      menuDataList: [],
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("permissionRules.form.enterMenuName"),
            trigger: "blur"
          }
        ],
        sort: [
          {
            required: true,
            message: this.$t("permissionRules.form.sortRequired"),
            trigger: "blur"
          }
        ]
      },
      statusOptions: [
        { value: "M", label: this.$t("permissionRules.menuType.directory") },
        { value: "C", label: this.$t("permissionRules.menuType.menu") },
        { value: "A", label: this.$t("permissionRules.menuType.button") }
      ],
      showStatus: [
        { label: this.$t("common.show"), value: true },
        { label: this.$t("common.hide"), value: false }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 点击图标
    addIcon() {
      const _this = this;
      _this.$modalIcon(function(icon) {
        _this.form.icon = icon;
      });
    },

    /** 查询菜单列表 */
    getList() {
      this.listLoading = true;
      menuListApi(this.queryParams).then(res => {
        let obj = {},
          menuList = [];
        res.forEach(item => {
          obj = item;
          obj.parentId = item.pid;
          obj.children = [];
          menuList.push(obj);
        });
        this.menuDataList = menuList;
        this.menuList = this.handleTree(menuList, "menuId");
        this.listLoading = false;
      });
    },

    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id ? node.id : 0,
        label: node.name ? node.name : "主目录",
        children: node.children
      };
    },

    /** 查询菜单下拉树结构 */
    getTreeselect() {
      this.menuOptions = [];
      const menu = { menuId: 0, menuName: "主类目", children: [] };
      menu.children = this.handleTree(this.menuDataList, "menuId");
      this.menuOptions.push(menu);
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        menuId: "",
        parentId: 0,
        name: "",
        icon: "",
        menuType: "M",
        sort: 0,
        isShow: true,
        component: "",
        perms: ""
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = { name: "", menuType: "" };
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != null && row.id) {
        this.form.pid = row.id;
      } else {
        this.form.pid = 0;
      }
      this.open = true;
      this.title = this.$t("permissionRules.actions.add");
    },

    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      const loading = this.$loading({
        lock: true,
        text: "Loading"
      });
      this.reset();
      this.getTreeselect();
      menuInfo(row.id).then(response => {
        this.form = response;
        this.open = true;
        this.title = this.$t("permissionRules.actions.edit");
        loading.close();
      });
    },

    /** 提交按钮 */
    submitForm: Debounce(function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            menuUpdate(this.form).then(response => {
              this.$modal.msgSuccess(this.$t("common.editSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            menuAdd(this.form).then(response => {
              this.$modal.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    }),

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm(this.$t("common.confirmDelete").replace("{name}", row.name))
        .then(() => {
          return menuDelete(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess(this.$t("common.deleteSuccess"));
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss">
.mb8 {
  margin-bottom: 8px;
}
.type_tag {
  display: inline-block;
  height: 32px;
  padding: 0 10px;
  line-height: 30px;
  font-size: 12px;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
}
.two {
  background: rgba(239, 156, 32, 0.1);
  color: rgba(239, 156, 32, 1);
}
.one {
  background: rgba(75, 202, 213, 0.1);
  color: rgba(75, 202, 213, 1);
}
.three {
  color: rgba(120, 128, 160, 1);
  background: rgba(120, 128, 160, 0.1);
}
</style>
