import 'dart:convert';
import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:milestone/models/category.dart';
import 'package:path/path.dart' as path;

void main() {
  group('Brand Model JSON Parsing Tests', () {
    late Map<String, dynamic> testData;
    late Map<String, dynamic> testDataList;

    setUp(() {
      // 从文件加载测试数据
      testData = _loadTestJson('brand.json');
      testDataList = _loadTestJson('category_list.json');
    });

    test('Test BrandModel Parsing', () {
      final brand = ProductCategory.fromJson(testData);

      // 验证基本字符串字段
      expect(brand.id, 33);
      expect(brand.name, "e.l.f. Cosmetics");
      expect(brand.gmtCreate, DateTime.parse("2025-06-17 14:54:48"));
      expect(brand.gmtModified, DateTime.parse("2025-06-17 15:08:01"));
    });

    test("test list", () {
      final list = ProductCategoryListResponse.fromJson(testDataList);
      expect(list.page, 1);
    });
  });
}

// 加载测试 JSON 文件的辅助函数
Map<String, dynamic> _loadTestJson(String fileName) {
  final testDir = path.join(Directory.current.path, 'test');
  final file = File(path.join(testDir, fileName));

  if (!file.existsSync()) {
    throw Exception('Test JSON file not found at ${file.path}');
  }

  final jsonString = file.readAsStringSync();
  return json.decode(jsonString) as Map<String, dynamic>;
}